# News-LLM 项目部署指南

## 🏗️ 项目服务组件分析

### 📊 核心服务组件：

1. **PostgreSQL数据库** - 主数据存储
2. **Redis** - 消息队列和缓存
3. **Miniflux** - RSS聚合器
4. **AI服务** - 多个Python进程
5. **爬虫服务** - Python进程
6. **Next.js前端** - Web应用
7. **Nginx** - 反向代理（可选）

## 💻 服务器配置建议

### 🎯 小规模部署（1000-5000篇文章/天）

#### 配置推荐：
- **CPU**: 4核心 (4 vCPU)
- **内存**: 8GB RAM
- **存储**: 100GB SSD
- **带宽**: 10Mbps
- **价格**: 约¥300-500/月

#### 资源分配：
```bash
PostgreSQL:     2GB RAM, 1 CPU核心
Redis:          512MB RAM
Miniflux:       1GB RAM, 1 CPU核心
AI服务:         3GB RAM, 1.5 CPU核心
前端+Nginx:     1GB RAM, 0.5 CPU核心
系统预留:       512MB RAM
```

### 🚀 中等规模部署（5000-20000篇文章/天）

#### 配置推荐：
- **CPU**: 8核心 (8 vCPU)
- **内存**: 16GB RAM
- **存储**: 200GB SSD
- **带宽**: 20Mbps
- **价格**: 约¥600-1000/月

#### 资源分配：
```bash
PostgreSQL:     4GB RAM, 2 CPU核心
Redis:          1GB RAM
Miniflux:       2GB RAM, 1 CPU核心
AI服务:         6GB RAM, 3 CPU核心
爬虫服务:       2GB RAM, 1 CPU核心
前端+Nginx:     1GB RAM, 1 CPU核心
```

### 🔥 大规模部署（>20000篇文章/天）

#### 配置推荐：
- **CPU**: 16核心 (16 vCPU)
- **内存**: 32GB RAM
- **存储**: 500GB SSD
- **带宽**: 50Mbps
- **价格**: 约¥1200-2000/月

#### 或者分布式部署：
```bash
数据库服务器:   8GB RAM, 4 CPU核心
AI处理服务器:   16GB RAM, 8 CPU核心
Web服务器:      8GB RAM, 4 CPU核心
```

## 🐳 Docker部署方案

### docker-compose.yml
```yaml
version: '3.8'

services:
  # PostgreSQL数据库
  postgres:
    image: postgres:15
    container_name: news-llm-postgres
    environment:
      POSTGRES_DB: newsllm
      POSTGRES_USER: newsllm
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 4G
          cpus: '2'

  # Redis缓存和队列
  redis:
    image: redis:7-alpine
    container_name: news-llm-redis
    command: redis-server --requirepass ${REDIS_PASSWORD}
    ports:
      - "6379:6379"
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'

  # Miniflux RSS聚合器
  miniflux:
    build: ./miniflux-chinese
    container_name: news-llm-miniflux
    environment:
      DATABASE_URL: postgres://newsllm:${POSTGRES_PASSWORD}@postgres/newsllm?sslmode=disable
      RUN_MIGRATIONS: 1
      CREATE_ADMIN: 1
      ADMIN_USERNAME: admin
      ADMIN_PASSWORD: ${MINIFLUX_PASSWORD}
    ports:
      - "8080:8080"
    depends_on:
      - postgres
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1'

  # AI处理服务
  ai-service:
    build: ./ai-service
    container_name: news-llm-ai
    environment:
      DATABASE_URL: postgresql://newsllm:${POSTGRES_PASSWORD}@postgres:5432/newsllm
      REDIS_URL: redis://:${REDIS_PASSWORD}@redis:6379
      QWEN_API_KEY: ${QWEN_API_KEY}
      WORKER_POOL_SIZE: 4
      MAX_CONCURRENT_REQUESTS: 10
    depends_on:
      - postgres
      - redis
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 6G
          cpus: '3'

  # 爬虫服务
  crawler:
    build: ./crawler
    container_name: news-llm-crawler
    environment:
      DATABASE_URL: postgresql://newsllm:${POSTGRES_PASSWORD}@postgres:5432/newsllm
      MINIFLUX_URL: http://miniflux:8080
      MINIFLUX_USERNAME: admin
      MINIFLUX_PASSWORD: ${MINIFLUX_PASSWORD}
    depends_on:
      - postgres
      - miniflux
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1'

  # Next.js前端
  frontend:
    build: ./news-llm-frontend
    container_name: news-llm-frontend
    environment:
      DATABASE_URL: postgresql://newsllm:${POSTGRES_PASSWORD}@postgres:5432/newsllm
      NEXTAUTH_URL: https://your-domain.com
      NEXTAUTH_SECRET: ${NEXTAUTH_SECRET}
    ports:
      - "3000:3000"
    depends_on:
      - postgres
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1'

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: news-llm-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - frontend
      - miniflux
    restart: unless-stopped

volumes:
  postgres_data:
```

## 🚀 部署脚本

### deploy.sh
```bash
#!/bin/bash
set -e

echo "🚀 开始部署News-LLM项目..."

# 1. 检查环境
echo "📋 检查部署环境..."
command -v docker >/dev/null 2>&1 || { echo "请先安装Docker"; exit 1; }
command -v docker-compose >/dev/null 2>&1 || { echo "请先安装Docker Compose"; exit 1; }

# 2. 创建环境变量文件
if [ ! -f .env.prod ]; then
    echo "📝 创建生产环境配置..."
    cat > .env.prod << EOF
POSTGRES_PASSWORD=$(openssl rand -base64 32)
REDIS_PASSWORD=$(openssl rand -base64 32)
MINIFLUX_PASSWORD=$(openssl rand -base64 32)
NEXTAUTH_SECRET=$(openssl rand -base64 32)
QWEN_API_KEY=your_qwen_api_key_here
EOF
    echo "⚠️  请编辑 .env.prod 文件，设置正确的API密钥"
    exit 1
fi

# 3. 构建和启动服务
echo "🔨 构建Docker镜像..."
docker-compose --env-file .env.prod build

echo "🚀 启动所有服务..."
docker-compose --env-file .env.prod up -d

# 4. 等待服务启动
echo "⏳ 等待服务启动..."
sleep 30

# 5. 检查服务状态
echo "🔍 检查服务状态..."
docker-compose --env-file .env.prod ps

# 6. 初始化数据
echo "🗄️ 初始化数据库..."
docker-compose --env-file .env.prod exec postgres psql -U newsllm -d newsllm -c "SELECT version();"

echo "✅ 部署完成！"
echo "📱 前端访问: http://your-domain.com"
echo "🔧 Miniflux管理: http://your-domain.com:8080"
```

## 📊 监控和维护

### 监控脚本 monitor.sh
```bash
#!/bin/bash

# 检查服务状态
echo "=== 服务状态 ==="
docker-compose ps

# 检查资源使用
echo "=== 资源使用 ==="
docker stats --no-stream

# 检查队列状态
echo "=== 队列状态 ==="
docker-compose exec redis redis-cli -a $REDIS_PASSWORD LLEN queue:new_articles
docker-compose exec redis redis-cli -a $REDIS_PASSWORD HGETALL stats:ai_processing

# 检查数据库状态
echo "=== 数据库状态 ==="
docker-compose exec postgres psql -U newsllm -d newsllm -c "
SELECT 
    COUNT(*) as total_articles,
    COUNT(*) FILTER (WHERE ai_processed = true) as processed_articles,
    AVG(ai_score) as avg_score
FROM entries;"
```

## 💰 成本估算

### 云服务商价格对比：

| 配置 | 阿里云ECS | 腾讯云CVM | AWS EC2 | 华为云ECS |
|------|-----------|-----------|---------|-----------|
| 4核8G | ¥400/月 | ¥380/月 | $60/月 | ¥350/月 |
| 8核16G | ¥800/月 | ¥750/月 | $120/月 | ¥700/月 |
| 16核32G | ¥1600/月 | ¥1500/月 | $240/月 | ¥1400/月 |

### 推荐方案：
- **初期**: 4核8G配置，约¥400/月
- **成长期**: 8核16G配置，约¥800/月
- **成熟期**: 考虑分布式部署或升级到16核32G

## 🔧 部署检查清单

- [ ] 服务器配置满足要求
- [ ] Docker和Docker Compose已安装
- [ ] 域名和SSL证书配置
- [ ] 环境变量文件配置完成
- [ ] 数据库备份策略制定
- [ ] 监控和日志收集配置
- [ ] 防火墙和安全组配置
- [ ] 自动化部署脚本测试

**建议从4核8G开始，根据实际使用情况逐步扩容！** 🎯

## 🧠 AI处理系统架构详解

### 🔄 触发机制

#### 1. 数据库触发器
```sql
-- 当Miniflux插入新文章时自动触发
CREATE TRIGGER trigger_notify_new_entry
    AFTER INSERT ON entries
    FOR EACH ROW
    EXECUTE FUNCTION notify_new_entry();
```

#### 2. 事件流程
```
新文章插入 → 数据库触发器 → PostgreSQL NOTIFY → Redis队列 → AI工作器池 → 处理结果存储
```

### 🏗️ 处理架构

#### 三种处理模式：

1. **Queue模式（推荐）**
   - 使用Redis队列缓冲
   - 支持多工作器并发
   - 高可靠性，支持重试
   - 适合大数据量

2. **Direct模式**
   - 直接实时处理
   - 低延迟
   - 适合小规模

3. **Hybrid模式**
   - 高优先级文章直接处理
   - 普通文章队列处理

### 📊 大数据量处理策略

#### 1. 队列系统
```python
# 多个队列分级处理
QUEUE_NEW_ARTICLES = "queue:new_articles"      # 新文章队列
QUEUE_HIGH_PRIORITY = "queue:high_priority"    # 高优先级队列
QUEUE_PROCESSING = "queue:processing"          # 处理中队列
QUEUE_FAILED = "queue:failed"                  # 失败队列
QUEUE_COMPLETED = "queue:completed"            # 完成队列
```

#### 2. 并发处理配置
```python
# 配置参数
WORKER_POOL_SIZE = 4                    # 工作器数量
MAX_CONCURRENT_REQUESTS = 10            # 最大并发请求
BATCH_SIZE = 10                         # 批处理大小
```

#### 3. 优先级机制
```python
# 根据关键词计算优先级
TRIGGER_PRIORITY_KEYWORDS = ["突发", "紧急", "重要", "breaking", "urgent"]
QUEUE_HIGH_PRIORITY_THRESHOLD = 7       # 高优先级阈值
```

#### 4. 重试和容错
```python
QUEUE_MAX_RETRIES = 3                   # 最大重试次数
QUEUE_RETRY_DELAY = 60                  # 重试延迟(秒)
AUTO_RESTART_ON_FAILURE = True          # 自动重启
```

#### 5. 性能限制
```python
MEMORY_LIMIT_MB = 1024                  # 内存限制
CPU_LIMIT_PERCENT = 80                  # CPU限制
CONTENT_MAX_LENGTH = 8000               # 内容长度限制
```

## 🚀 处理流程详解

### 步骤1: 事件触发
1. Miniflux插入新文章到`entries`表
2. 数据库触发器`notify_new_entry()`被触发
3. 通过`pg_notify()`发送通知到`new_entry_channel`

### 步骤2: 队列入队
1. `realtime_processor.py`监听数据库通知
2. 解析文章数据，计算优先级
3. 根据优先级推入不同队列：
   - 高优先级 → `queue:high_priority`
   - 普通优先级 → `queue:new_articles`

### 步骤3: 工作器处理
1. 多个`queue_processor.py`工作器并发运行
2. 从队列中取出文章（`brpoplpush`原子操作）
3. 移动到`queue:processing`防止丢失

### 步骤4: AI分析
1. 调用`AIProcessor.process_single_article()`
2. 执行摘要生成、分类、评分等AI处理
3. 调用通义千问API进行智能分析

### 步骤5: 结果存储
1. 成功：更新`entries`表的AI字段，移入`queue:completed`
2. 失败：重试或移入`queue:failed`

## 📈 大数据量优化策略

### 1. 水平扩展
```bash
# 启动多个工作器
python queue_processor.py --worker-id=worker_1 &
python queue_processor.py --worker-id=worker_2 &
python queue_processor.py --worker-id=worker_3 &
python queue_processor.py --worker-id=worker_4 &
```

### 2. 批量处理
```python
# 批量获取和处理文章
BATCH_SIZE = 20
articles = fetch_articles_batch(BATCH_SIZE)
results = process_articles_batch(articles)
```

### 3. 缓存机制
```python
CACHE_ENABLED = True
CACHE_TTL = 3600  # 1小时缓存
# 相似文章避免重复处理
```

### 4. 资源监控
```python
# 实时监控队列长度
redis-cli LLEN queue:new_articles
redis-cli HGETALL stats:ai_processing

# 性能统计
{
    "total_processed": 1250,
    "successful": 1180,
    "failed": 70,
    "avg_processing_time": 2.3,
    "queue_length": 45
}
```

## 🔧 不同规模配置建议

### 小规模（<1000篇/天）
```python
WORKER_POOL_SIZE = 2
MAX_CONCURRENT_REQUESTS = 5
PROCESSING_MODE = "direct"
```

### 中等规模（1000-10000篇/天）
```python
WORKER_POOL_SIZE = 4
MAX_CONCURRENT_REQUESTS = 10
PROCESSING_MODE = "queue"
BATCH_SIZE = 10
```

### 大规模（>10000篇/天）
```python
WORKER_POOL_SIZE = 8
MAX_CONCURRENT_REQUESTS = 20
PROCESSING_MODE = "hybrid"
BATCH_SIZE = 20
# 部署多台服务器
```

## 🚨 故障处理

### 队列堆积处理
```bash
# 检查队列长度
redis-cli LLEN queue:new_articles

# 增加工作器
export WORKER_POOL_SIZE=8

# 清理失败队列
redis-cli DEL queue:failed
```

### 性能监控
```bash
# 实时监控
tail -f logs/ai-service/queue_processor_*.log

# 访问Flower监控界面
http://localhost:5555
```

## 🔒 安全配置

### 1. 防火墙设置
```bash
# 只开放必要端口
ufw allow 22    # SSH
ufw allow 80    # HTTP
ufw allow 443   # HTTPS
ufw enable
```

### 2. SSL证书配置
```bash
# 使用Let's Encrypt
certbot --nginx -d your-domain.com
```

### 3. 数据库安全
```bash
# 修改默认密码
ALTER USER newsllm WITH PASSWORD 'strong_password_here';

# 限制连接
# postgresql.conf
listen_addresses = 'localhost'
max_connections = 100
```

### 4. Redis安全
```bash
# 设置密码
requirepass your_redis_password_here

# 禁用危险命令
rename-command FLUSHDB ""
rename-command FLUSHALL ""
```

## 📋 维护任务

### 日常维护
```bash
# 每日备份数据库
pg_dump -U newsllm newsllm > backup_$(date +%Y%m%d).sql

# 清理日志文件
find logs/ -name "*.log" -mtime +7 -delete

# 检查磁盘空间
df -h

# 检查内存使用
free -h
```

### 周期性任务
```bash
# 每周重启服务（可选）
docker-compose restart

# 清理Docker镜像
docker system prune -f

# 更新系统
apt update && apt upgrade -y
```

---

**更新时间**: 2025-01-27
**文档版本**: v1.0
