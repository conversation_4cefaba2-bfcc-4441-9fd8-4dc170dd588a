# News-LLM 付费功能设计文档

## 📋 功能概述

本文档介绍了News-LLM平台的付费订阅功能，包括页面设计、组件架构和API接口。

## 🎯 核心功能

### 1. 付费订阅制度
- **免费用户**: 10个订阅源，评分筛选上限3.9★，高分内容延迟解锁
- **付费用户**: 100个订阅源，评分筛选上限5.0★，所有内容实时解锁
- **试用机制**: 7天免费试用，每账号限1次

### 2. 高分内容延迟解锁
- **触发条件**: 新闻评分≥4.0★
- **解锁模式**: D+1（次日解锁）或T+24h（24小时后解锁）
- **用户体验**: 锁定态显示蒙层+解锁CTA

## 📱 页面设计

### 1. 定价页 (`/pricing`)
**功能**: 展示付费计划和真实价格
**特点**:
- 中国/海外市场切换
- 月付/年付方案对比
- 权益矩阵清晰展示
- 试用入口引导

**价格方案**:
- 中国: ¥19/月, ¥128/年
- 海外: $6.99/月, $59/年

### 2. 试用页 (`/trial`)
**功能**: 独立的试用领取页面
**特点**:
- 权益说明详细
- 到期规则透明
- 每账号限1次
- 自动跳转首页

### 3. 演示页 (`/demo-payment`)
**功能**: 展示所有付费功能组件
**包含**:
- 锁定内容横幅
- 订阅状态卡片
- 锁定新闻卡片
- 支付弹窗

## 🧩 组件架构

### 1. LockedNewsCard
**用途**: 显示需要付费解锁的新闻
**特点**:
- 蒙层遮挡效果
- 解锁倒计时
- 双CTA设计（付费+试用）
- 票券风格UI

### 2. LockedContentBanner
**用途**: 顶部提示今日锁定内容数量
**特点**:
- 渐变背景吸引注意
- 实时数量统计
- 快捷操作按钮
- 可关闭设计

### 3. SubscriptionStatus
**用途**: 显示用户当前订阅状态
**包含**:
- 订阅类型和到期时间
- 订阅源使用情况
- 权益对比
- 快捷操作按钮

### 4. PaymentModal
**用途**: 统一的支付流程弹窗
**流程**:
1. 选择方案（月付/年付）
2. 选择支付方式（微信/支付宝/Stripe）
3. 处理支付
4. 成功反馈

## 🔌 API接口

### 1. 试用相关
```typescript
// 领取试用
POST /api/trial/claim
{
  "user_id": "123"
}

// 响应
{
  "success": true,
  "trial_end_at": "2024-01-07T00:00:00Z"
}
```

### 2. 订阅状态
```typescript
// 获取订阅状态
GET /api/user-subscription?user_id=123

// 响应
{
  "status": "trialing", // free|trialing|active|canceled|expired
  "trial_availed": true,
  "subscription_sources_count": 5,
  "subscription_sources_limit": 100,
  "trial_end_at": "2024-01-07T00:00:00Z"
}
```

### 3. 支付相关
```typescript
// 创建订单
POST /api/checkout
{
  "user_id": "123",
  "plan_id": "pro-monthly",
  "market": "cn",
  "payment_method": "wechat"
}

// 响应
{
  "success": true,
  "order": {
    "order_id": "456",
    "amount": 1900,
    "currency": "CNY",
    "qr_code": "data:image/png;base64,..."
  }
}
```

### 4. 锁定内容
```typescript
// 获取锁定内容数量
GET /api/news/locked-count?user_id=123

// 响应
{
  "count": 5,
  "is_pro": false,
  "unlock_mode": "D+1"
}
```

## 🎨 UI设计风格

### 1. 票券风格
- 黑白配色为主
- 阴影效果突出层次
- 等宽字体增强科技感
- 边框和分割线清晰

### 2. 交互设计
- 悬停效果增强反馈
- 渐变背景吸引注意
- 蒙层遮挡营造紧迫感
- 双CTA降低转化阻力

### 3. 响应式适配
- 桌面端左右布局
- 移动端上下堆叠
- 关键信息优先显示
- 操作按钮易于点击

## 🔧 技术实现

### 1. 权限判定逻辑
```typescript
function isLockedForFree(user: User, article: Article): boolean {
  // 评分低于4.0不锁定
  if (article.aiScore < 4.0) return false
  
  // 付费/试用用户不锁定
  if (user.isPro || user.isTrialing) return false
  
  // D+1模式：当天发布的锁定
  const today = new Date().toDateString()
  const publishDate = new Date(article.publishedAt).toDateString()
  return today === publishDate
}
```

### 2. 订阅源限制
```typescript
function canSubscribeFeed(user: User): boolean {
  const limit = user.isPro ? 100 : 10
  return user.subscriptionCount < limit
}
```

### 3. 评分筛选权限
```typescript
function getMaxRatingFilter(user: User): number {
  return user.isPro ? 5.0 : 3.9
}
```

## 📊 数据库设计

### 核心表结构
```sql
-- 用户订阅状态
user_subscriptions (
  id, user_id, plan_id, status,
  trial_availed, trial_end_at,
  current_period_end_at, auto_renew
)

-- 付费计划
plans (
  id, market, name, period,
  price_cents, currency, is_active
)

-- 订单记录
orders (
  id, user_id, plan_id, amount_cents,
  currency, provider, status
)
```

## 🚀 部署说明

### 1. 环境变量
```env
# 支付相关
WECHAT_PAY_APP_ID=your_app_id
ALIPAY_APP_ID=your_app_id
STRIPE_SECRET_KEY=your_secret_key

# 数据库
DATABASE_URL=postgresql://...
```

### 2. 初始化数据
```sql
-- 插入付费计划
INSERT INTO plans (market, name, period, price_cents, currency) VALUES
('cn', 'Pro', 'monthly', 1900, 'CNY'),
('cn', 'Pro', 'yearly', 12800, 'CNY'),
('us', 'Pro', 'monthly', 699, 'USD'),
('us', 'Pro', 'yearly', 5900, 'USD');
```

## 📈 监控指标

### 1. 业务指标
- 试用转化率
- 付费用户留存率
- 订阅源使用率
- 锁定内容点击率

### 2. 技术指标
- API响应时间
- 支付成功率
- 页面加载速度
- 错误率监控

## 🔄 后续优化

### 1. 功能增强
- T+24h解锁模式
- 多种支付方式
- 订阅管理页面
- 发票开具功能

### 2. 用户体验
- 支付流程优化
- 错误提示完善
- 加载状态改进
- 移动端适配

---

**本文档涵盖了News-LLM付费功能的完整设计和实现，为产品迭代提供技术指导。**
