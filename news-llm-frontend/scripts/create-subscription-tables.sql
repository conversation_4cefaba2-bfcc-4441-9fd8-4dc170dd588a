-- 创建用户订阅状态表
CREATE TABLE IF NOT EXISTS user_subscriptions (
  id SERIAL PRIMARY KEY,
  user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  plan_id INTEGER,
  status VARCHAR(20) NOT NULL DEFAULT 'free', -- 'free', 'trialing', 'active', 'expired', 'canceled'
  plan_name VARCHAR(50),
  trial_availed BOOLEAN NOT NULL DEFAULT false,
  trial_start_at TIMESTAMPTZ,
  trial_end_at TIMESTAMPTZ,
  current_period_start_at TIMESTAMPTZ,
  current_period_end_at TIMESTAMPTZ,
  auto_renew BOOLEAN NOT NULL DEFAULT false,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  UNIQUE(user_id)
);

-- 创建付费计划表
CREATE TABLE IF NOT EXISTS plans (
  id SERIAL PRIMARY KEY,
  market VARCHAR(10) NOT NULL, -- 'cn', 'global'
  name VARCHAR(50) NOT NULL, -- 'Pro'
  period VARCHAR(20) NOT NULL, -- 'monthly', 'yearly'
  price_cents INTEGER NOT NULL, -- 价格（分）
  currency VARCHAR(3) NOT NULL, -- 'CNY', 'USD'
  is_active BOOLEAN NOT NULL DEFAULT true,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  UNIQUE(market, name, period)
);

-- 创建订单表
CREATE TABLE IF NOT EXISTS orders (
  id SERIAL PRIMARY KEY,
  user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  plan_id INTEGER,
  amount_cents INTEGER NOT NULL,
  currency VARCHAR(3) NOT NULL,
  provider VARCHAR(20) NOT NULL, -- 'wechat', 'alipay', 'stripe'
  status VARCHAR(20) NOT NULL, -- 'pending', 'paid', 'failed'
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- 插入默认计划
INSERT INTO plans (market, name, period, price_cents, currency) VALUES
('cn', 'Pro', 'monthly', 1999, 'CNY'),
('cn', 'Pro', 'yearly', 19999, 'CNY'),
('global', 'Pro', 'monthly', 299, 'USD'),
('global', 'Pro', 'yearly', 2999, 'USD')
ON CONFLICT (market, name, period) DO NOTHING;

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_user_id ON user_subscriptions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_status ON user_subscriptions(status);
CREATE INDEX IF NOT EXISTS idx_orders_user_id ON orders(user_id);
CREATE INDEX IF NOT EXISTS idx_orders_status ON orders(status);

-- 创建更新时间触发器
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_user_subscriptions_updated_at 
    BEFORE UPDATE ON user_subscriptions 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_plans_updated_at 
    BEFORE UPDATE ON plans 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
