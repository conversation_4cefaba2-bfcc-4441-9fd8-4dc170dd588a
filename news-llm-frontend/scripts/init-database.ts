import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  console.log('🚀 开始初始化数据库...')

  try {
    // 1. 确保有管理员用户
    let adminUser = await prisma.users.findFirst({
      where: { is_admin: true }
    })

    if (!adminUser) {
      console.log('📝 创建管理员用户...')
      const hashedPassword = await bcrypt.hash('admin123', 10)
      adminUser = await prisma.users.create({
        data: {
          username: 'admin',
          email: '<EMAIL>',
          password: hashedPassword,
          is_admin: true,
          language: 'zh_CN',
          timezone: 'Asia/Shanghai'
        }
      })
      console.log('✅ 管理员用户创建成功')
    }

    // 2. 创建默认分类
    const categories = [
      { title: '科技', user_id: adminUser.id },
      { title: 'AI', user_id: adminUser.id },
      { title: '财经', user_id: adminUser.id },
      { title: '创业', user_id: adminUser.id },
      { title: '互联网', user_id: adminUser.id }
    ]

    console.log('📂 创建默认分类...')
    for (const category of categories) {
      await prisma.categories.upsert({
        where: {
          user_id_title: {
            user_id: category.user_id,
            title: category.title
          }
        },
        update: {},
        create: category
      })
    }
    console.log('✅ 默认分类创建完成')

    // 3. 创建示例订阅源
    const defaultCategory = await prisma.categories.findFirst({
      where: { user_id: adminUser.id, title: '科技' }
    })

    if (!defaultCategory) {
      throw new Error('默认分类创建失败')
    }

    const feeds = [
      {
        user_id: adminUser.id,
        category_id: defaultCategory.id,
        title: '36氪',
        feed_url: 'https://36kr.com/feed',
        site_url: 'https://36kr.com',
        description: '36氪 - 让创业更简单'
      },
      {
        user_id: adminUser.id,
        category_id: defaultCategory.id,
        title: '虎嗅网',
        feed_url: 'https://www.huxiu.com/rss/0.xml',
        site_url: 'https://www.huxiu.com',
        description: '虎嗅网 - 个性化商业资讯与观点交流平台'
      },
      {
        user_id: adminUser.id,
        category_id: defaultCategory.id,
        title: 'InfoQ',
        feed_url: 'https://www.infoq.cn/feed',
        site_url: 'https://www.infoq.cn',
        description: 'InfoQ - 促进软件开发及相关领域知识与创新的传播'
      }
    ]

    console.log('📡 创建示例订阅源...')
    for (const feed of feeds) {
      await prisma.feeds.upsert({
        where: {
          user_id_feed_url: {
            user_id: feed.user_id,
            feed_url: feed.feed_url
          }
        },
        update: {},
        create: feed
      })
    }
    console.log('✅ 示例订阅源创建完成')

    // 4. 创建示例新闻条目
    const createdFeeds = await prisma.feeds.findMany({
      where: { user_id: adminUser.id }
    })

    const sampleEntries = [
      {
        title: 'OpenAI发布GPT-5，多模态能力再次突破',
        content: 'OpenAI最新发布的GPT-5在多模态理解、推理能力和代码生成方面都有显著提升，支持更长的上下文窗口，能够处理更复杂的任务。这是AI领域的又一重大突破，将推动整个行业的发展。',
        ai_summary: 'OpenAI发布GPT-5，在多模态理解、推理和代码生成方面显著提升，支持更长上下文，是AI领域重大突破。',
        url: 'https://example.com/news/1',
        author: 'AI科技报道',
        published_at: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2小时前
        ai_score: 4.8,
        quality_score: 4.5,
        sentiment: 'positive',
        category: 'AI',
        tags: ['OpenAI', 'GPT-5', 'AI', '多模态'],
        reading_time: 5,
        ai_processed: true
      },
      {
        title: '量子计算重大突破：IBM实现1000量子比特处理器',
        content: 'IBM宣布成功开发出1000量子比特的量子处理器，这是量子计算领域的重大里程碑。新处理器在量子纠错和稳定性方面都有显著改进，为实用化量子计算奠定了基础。',
        ai_summary: 'IBM成功开发1000量子比特处理器，在量子纠错和稳定性方面显著改进，为实用化量子计算奠定基础。',
        url: 'https://example.com/news/2',
        author: '科技前沿',
        published_at: new Date(Date.now() - 4 * 60 * 60 * 1000), // 4小时前
        ai_score: 4.3,
        quality_score: 4.2,
        sentiment: 'positive',
        category: '科技',
        tags: ['IBM', '量子计算', '科技突破'],
        reading_time: 4,
        ai_processed: true
      },
      {
        title: '创业公司如何在AI时代找到机会',
        content: '在AI技术快速发展的今天，创业公司面临着前所未有的机遇和挑战。本文分析了几个关键策略，帮助创业者在激烈的市场竞争中找到自己的定位。',
        ai_summary: '分析AI时代创业公司面临的机遇挑战，提供关键策略帮助创业者找到市场定位。',
        url: 'https://example.com/news/3',
        author: '创业邦',
        published_at: new Date(Date.now() - 6 * 60 * 60 * 1000), // 6小时前
        ai_score: 3.9,
        quality_score: 3.8,
        sentiment: 'neutral',
        category: '创业',
        tags: ['创业', 'AI', '商业策略'],
        reading_time: 6,
        ai_processed: true
      },
      {
        title: '比特币突破10万美元大关，创历史新高',
        content: '受机构投资者大量买入推动，比特币价格首次突破10万美元，分析师认为这标志着加密货币市场进入新的发展阶段，但也提醒投资者注意风险。',
        ai_summary: '比特币突破10万美元创历史新高，受机构投资者推动，标志加密货币市场新阶段。',
        url: 'https://example.com/news/4',
        author: '财经观察',
        published_at: new Date(Date.now() - 8 * 60 * 60 * 1000), // 8小时前
        ai_score: 4.1,
        quality_score: 4.0,
        sentiment: 'positive',
        category: '财经',
        tags: ['比特币', '加密货币', '投资'],
        reading_time: 3,
        ai_processed: true
      }
    ]

    console.log('📰 创建示例新闻条目...')
    for (let i = 0; i < sampleEntries.length; i++) {
      const entry = sampleEntries[i]
      const feed = createdFeeds[i % createdFeeds.length]
      
      await prisma.entries.create({
        data: {
          ...entry,
          user_id: adminUser.id,
          feed_id: feed.id,
          hash: `hash_${Date.now()}_${i}`,
          changed_at: entry.published_at,
          status: 'unread'
        }
      })
    }
    console.log('✅ 示例新闻条目创建完成')

    // 5. 为管理员创建系统推荐订阅
    console.log('🔗 创建系统推荐订阅关系...')
    for (const feed of createdFeeds) {
      await prisma.user_feed_subscriptions.upsert({
        where: {
          user_id_feed_id: {
            user_id: adminUser.id,
            feed_id: feed.id
          }
        },
        update: {},
        create: {
          user_id: adminUser.id,
          feed_id: feed.id,
          is_system_curated: true
        }
      })
    }
    console.log('✅ 系统推荐订阅关系创建完成')

    console.log('🎉 数据库初始化完成！')
    console.log(`👤 管理员账号: admin / admin123`)
    console.log(`📊 创建了 ${categories.length} 个分类`)
    console.log(`📡 创建了 ${feeds.length} 个订阅源`)
    console.log(`📰 创建了 ${sampleEntries.length} 条新闻`)

  } catch (error) {
    console.error('❌ 数据库初始化失败:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

main()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
