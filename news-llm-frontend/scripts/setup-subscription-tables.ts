import { PrismaClient } from '@prisma/client'
import fs from 'fs'
import path from 'path'

const prisma = new PrismaClient()

async function main() {
  console.log('🚀 开始创建订阅相关表...')

  try {
    // 读取SQL文件
    const sqlPath = path.join(__dirname, 'create-subscription-tables.sql')
    const sql = fs.readFileSync(sqlPath, 'utf8')

    // 执行SQL
    await prisma.$executeRawUnsafe(sql)

    console.log('✅ 订阅相关表创建成功！')
    console.log('📊 已创建表：')
    console.log('  - user_subscriptions (用户订阅状态)')
    console.log('  - plans (付费计划)')
    console.log('  - orders (订单记录)')
    console.log('💰 已插入默认计划：')
    console.log('  - 中国市场：月付 ¥19.99，年付 ¥199.99')
    console.log('  - 全球市场：月付 $2.99，年付 $29.99')

  } catch (error) {
    console.error('❌ 创建订阅表失败:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

main()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
