'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import Link from 'next/link'

interface SubscriptionData {
  status: 'free' | 'trialing' | 'active' | 'canceled' | 'expired'
  plan_name?: string
  current_period_end_at?: string
  trial_end_at?: string
  trial_availed: boolean
  subscription_sources_count: number
  subscription_sources_limit: number
}

export default function SubscriptionStatus() {
  const { data: session } = useSession()
  const [subscription, setSubscription] = useState<SubscriptionData | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchSubscription = async () => {
      if (!session?.user?.id) {
        setLoading(false)
        return
      }

      try {
        const response = await fetch('/api/subscription/status')
        if (response.ok) {
          const data = await response.json()
          setSubscription(data)
        }
      } catch (error) {
        console.error('获取订阅状态失败:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchSubscription()
  }, [session])

  if (loading) {
    return (
      <div className="bg-white border border-gray-200 p-4">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
          <div className="h-3 bg-gray-200 rounded w-3/4"></div>
        </div>
      </div>
    )
  }

  if (!subscription) {
    return null
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const getStatusInfo = () => {
    switch (subscription.status) {
      case 'free':
        return {
          title: '免费版',
          subtitle: '基础功能',
          color: 'bg-gray-100 text-gray-800',
          icon: '🆓'
        }
      case 'trialing':
        return {
          title: '试用中',
          subtitle: subscription.trial_end_at ? `试用至 ${formatDate(subscription.trial_end_at)}` : '7天试用',
          color: 'bg-blue-100 text-blue-800',
          icon: '🎁'
        }
      case 'active':
        return {
          title: '专业版',
          subtitle: subscription.current_period_end_at ? `续费至 ${formatDate(subscription.current_period_end_at)}` : '已激活',
          color: 'bg-green-100 text-green-800',
          icon: '⭐'
        }
      case 'canceled':
        return {
          title: '已取消',
          subtitle: subscription.current_period_end_at ? `服务至 ${formatDate(subscription.current_period_end_at)}` : '已取消订阅',
          color: 'bg-yellow-100 text-yellow-800',
          icon: '⚠️'
        }
      case 'expired':
        return {
          title: '已过期',
          subtitle: '订阅已过期',
          color: 'bg-red-100 text-red-800',
          icon: '❌'
        }
      default:
        return {
          title: '未知状态',
          subtitle: '',
          color: 'bg-gray-100 text-gray-800',
          icon: '❓'
        }
    }
  }

  const statusInfo = getStatusInfo()
  const isPro = subscription.status === 'active' || subscription.status === 'trialing'

  return (
    <div className="bg-white border-2 border-black shadow-[2px_2px_0px_0px_rgba(0,0,0,1)]">
      {/* 头部状态 */}
      <div className="bg-black text-white p-3 flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <span className="text-lg">{statusInfo.icon}</span>
          <div>
            <div className="font-bold font-mono text-sm">SUBSCRIPTION</div>
            <div className="text-xs opacity-80">订阅状态</div>
          </div>
        </div>
        <div className={`px-3 py-1 rounded text-xs font-mono font-bold ${statusInfo.color}`}>
          {statusInfo.title}
        </div>
      </div>

      {/* 详细信息 */}
      <div className="p-4">
        <div className="mb-4">
          <div className="text-sm text-gray-600 mb-1">{statusInfo.subtitle}</div>
          
          {/* 订阅源使用情况 */}
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-600">订阅源使用:</span>
            <span className="font-mono font-bold">
              {subscription.subscription_sources_count} / {subscription.subscription_sources_limit}
            </span>
          </div>
          
          {/* 进度条 */}
          <div className="w-full bg-gray-200 h-2 mt-2 border border-gray-300">
            <div 
              className={`h-full transition-all duration-300 ${
                subscription.subscription_sources_count >= subscription.subscription_sources_limit 
                  ? 'bg-red-500' 
                  : isPro 
                    ? 'bg-green-500' 
                    : 'bg-blue-500'
              }`}
              style={{ 
                width: `${Math.min(100, (subscription.subscription_sources_count / subscription.subscription_sources_limit) * 100)}%` 
              }}
            ></div>
          </div>
        </div>

        {/* 权益对比 */}
        <div className="space-y-2 mb-4">
          <div className="flex items-center justify-between text-xs">
            <span className="text-gray-600">高分内容解锁:</span>
            <span className={`font-mono font-bold ${isPro ? 'text-green-600' : 'text-red-600'}`}>
              {isPro ? '实时' : '延迟'}
            </span>
          </div>
          <div className="flex items-center justify-between text-xs">
            <span className="text-gray-600">评分筛选上限:</span>
            <span className="font-mono font-bold">
              {isPro ? '5.0★' : '3.9★'}
            </span>
          </div>

        </div>

        {/* 操作按钮 */}
        <div className="space-y-2">
          {subscription.status === 'free' && (
            <>
              {!subscription.trial_availed && (
                <Link
                  href="/trial"
                  className="block w-full bg-blue-600 text-white text-center py-2 font-mono font-bold text-sm hover:bg-blue-700 transition-all shadow-[1px_1px_0px_0px_rgba(0,0,0,0.3)]"
                >
                  领取7天试用
                </Link>
              )}
            </>
          )}
          
          {subscription.status === 'trialing' && (
            <Link
              href="/pricing"
              className="block w-full bg-green-600 text-white text-center py-2 font-mono font-bold text-sm hover:bg-green-700 transition-all shadow-[1px_1px_0px_0px_rgba(0,0,0,0.3)]"
            >
              转为正式订阅
            </Link>
          )}
          
          {(subscription.status === 'active' || subscription.status === 'canceled') && (
            <Link
              href="/my-subscriptions"
              className="block w-full bg-gray-600 text-white text-center py-2 font-mono font-bold text-sm hover:bg-gray-700 transition-all shadow-[1px_1px_0px_0px_rgba(0,0,0,0.3)]"
            >
              管理订阅
            </Link>
          )}
          
          {subscription.status === 'expired' && (
            <>
              <Link
                href="/pricing"
                className="block w-full bg-black text-white text-center py-2 font-mono font-bold text-sm hover:bg-gray-800 transition-all shadow-[1px_1px_0px_0px_rgba(0,0,0,0.3)]"
              >
                重新订阅
              </Link>
              {!subscription.trial_availed && (
                <Link
                  href="/trial"
                  className="block w-full bg-blue-600 text-white text-center py-2 font-mono font-bold text-sm hover:bg-blue-700 transition-all shadow-[1px_1px_0px_0px_rgba(0,0,0,0.3)]"
                >
                  领取试用
                </Link>
              )}
            </>
          )}
        </div>

        {/* 提示信息 */}
        {subscription.status === 'trialing' && (
          <div className="mt-3 p-2 bg-blue-50 border border-blue-200 text-xs text-blue-700">
            💡 试用期结束后将自动回到免费版，不会产生费用
          </div>
        )}
        
        {subscription.subscription_sources_count >= subscription.subscription_sources_limit && (
          <div className="mt-3 p-2 bg-red-50 border border-red-200 text-xs text-red-700">
            ⚠️ 订阅源已达上限，{isPro ? '升级可获得更多配额' : '请升级到专业版'}
          </div>
        )}
      </div>
    </div>
  )
}
