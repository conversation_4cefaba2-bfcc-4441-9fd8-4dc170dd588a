'use client'

import { useState } from 'react'
import Link from 'next/link'

interface LockedNewsCardProps {
  id: string
  title: string
  summary: string
  source: string
  publishedAt: string
  aiScore: number
  tags: string[]
  readTime: number
  isSystem: boolean
  unlockAt?: string // 解锁时间 (T+24h模式)
  unlockMode: 'D+1' | 'T+24h'
  onUpgrade: () => void
  onTrial: () => void
}

export default function LockedNewsCard({
  id,
  title,
  summary,
  source,
  publishedAt,
  aiScore,
  tags,
  readTime,
  isSystem,
  unlockAt,
  unlockMode,
  onUpgrade,
  onTrial
}: LockedNewsCardProps) {
  const [showPaymentModal, setShowPaymentModal] = useState(false)

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))
    const diffInHours = Math.floor(diffInMinutes / 60)
    const diffInDays = Math.floor(diffInHours / 24)

    if (diffInMinutes < 1) return '刚刚'
    if (diffInMinutes < 60) return `${diffInMinutes}分钟前`
    if (diffInHours < 24) return `${diffInHours}小时前`
    if (diffInDays < 7) return `${diffInDays}天前`
    return date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' })
  }

  const getUnlockText = () => {
    if (unlockMode === 'D+1') {
      return 'D+1 解锁'
    } else if (unlockAt) {
      const unlockTime = new Date(unlockAt)
      const now = new Date()
      const diffInHours = Math.ceil((unlockTime.getTime() - now.getTime()) / (1000 * 60 * 60))
      if (diffInHours <= 0) return '即将解锁'
      if (diffInHours < 24) return `剩 ${diffInHours} 小时解锁`
      return `剩 ${Math.ceil(diffInHours / 24)} 天解锁`
    }
    return '待解锁'
  }

  const getScoreColor = (score: number) => {
    if (score >= 4.5) return 'bg-green-500'
    if (score >= 4.0) return 'bg-blue-500'
    return 'bg-gray-500'
  }

  // 截断摘要显示（锁定态显示前20-30字）
  const visibleSummary = summary.length > 25 ? summary.substring(0, 25) : summary
  const hiddenSummary = summary.length > 25 ? summary.substring(25) : ''

  return (
    <>
      <article className="bg-white border border-black shadow-[3px_3px_0px_0px_rgba(0,0,0,1)] hover:shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] transition-all duration-300 relative overflow-hidden">
        {/* 票券头部 - 优化重复信息 */}
        <div className="bg-black text-white p-2 flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="bg-yellow-500 text-black px-2 py-1 font-mono text-xs font-bold">
              🔒 {getUnlockText()}
            </div>
            <div className="border-l border-gray-400 pl-3">
              <div className="text-xs font-mono opacity-80">SOURCE</div>
              <div className="text-sm font-bold tracking-wide flex items-center">
                {source}
                <span className="ml-2 text-xs bg-gray-700 px-1 py-0.5 rounded">
                  {isSystem ? '系统' : '订阅'}
                </span>
              </div>
            </div>
          </div>
          <div className="text-right">
            <div className="text-xs font-mono opacity-80">RATING</div>
            <div className="text-lg font-bold flex items-center">
              <span className={`w-3 h-3 rounded-full ${getScoreColor(aiScore)} mr-1`}></span>
              ★ {aiScore}
            </div>
          </div>
        </div>

        {/* 票券主体 - 优化布局 */}
        <div className="p-3">
          {/* 简化的时间戳 */}
          <div className="flex items-center justify-between mb-3 pb-2 border-b border-gray-200">
            <div className="text-xs font-mono text-gray-500">
              {formatDate(publishedAt)}
            </div>
          </div>

          {/* 标题区域 */}
          <div className="mb-3">
            <h2 className="text-xl font-bold text-black mb-1 line-clamp-2 leading-tight">
              {title}
            </h2>
            <div className="w-12 h-0.5 bg-black"></div>
          </div>

          {/* 摘要 - 部分可见+蒙层效果 */}
          <div className="relative mb-3">
            <p className="text-gray-700 mb-3 line-clamp-2 leading-relaxed text-sm">
              <span>{visibleSummary}</span>
              {hiddenSummary && (
                <span className="relative inline-block">
                  <span className="text-gray-300 blur-[1px] select-none opacity-60">
                    {hiddenSummary.substring(0, 15)}
                  </span>
                  <span className="absolute inset-0 bg-gradient-to-r from-transparent via-white/50 to-white pointer-events-none"></span>
                  <span className="ml-1 text-xs text-gray-400 font-mono">[蒙层遮挡]</span>
                </span>
              )}
            </p>
          </div>

          {/* 标签区域 */}
          <div className="flex flex-wrap gap-2 mb-3">
            {tags.slice(0, 2).map((tag, tagIndex) => (
              <span
                key={tag}
                className="px-2 py-1 bg-gray-100 text-black text-xs font-mono font-bold border border-gray-300"
              >
                #{tagIndex + 1} {tag}
              </span>
            ))}
            {tags.length > 2 && (
              <span className="px-2 py-1 bg-gray-100 text-gray-500 text-xs font-mono font-bold border border-gray-300">
                +{tags.length - 2}
              </span>
            )}
          </div>

          {/* 操作区域 - 简化布局 */}
          <div className="border-t border-dashed border-gray-300 pt-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <button
                  onClick={() => setShowPaymentModal(true)}
                  className="bg-black text-white px-4 py-2 font-mono text-sm font-bold hover:bg-gray-800 transition-all border border-black shadow-[1px_1px_0px_0px_rgba(0,0,0,0.3)]"
                >
                  立即解锁 ¥19/月 →
                </button>

                <button
                  onClick={onTrial}
                  className="flex items-center space-x-1 text-gray-600 hover:text-black px-3 py-2 border border-gray-300 hover:border-black transition-all font-mono text-sm font-bold"
                >
                  <span>🎁</span>
                  <span>试用7天</span>
                </button>
              </div>
              <div className="text-xs font-mono text-gray-500">
                {readTime} MIN READ
              </div>
            </div>
          </div>
        </div>


      </article>

      {/* 支付弹窗 */}
      {showPaymentModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white border-2 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,1)] max-w-md w-full">
            <div className="bg-black text-white p-4 flex items-center justify-between">
              <h3 className="text-lg font-bold font-mono">解锁高分内容</h3>
              <button
                onClick={() => setShowPaymentModal(false)}
                className="text-white hover:text-gray-300 text-xl"
              >
                ×
              </button>
            </div>
            <div className="p-6">
              <div className="text-center mb-6">
                <div className="text-4xl mb-2">⭐</div>
                <h4 className="text-xl font-bold text-black mb-2">
                  升级到专业版
                </h4>
                <p className="text-gray-600 text-sm">
                  解锁所有≥4.0★高分内容，享受完整阅读体验
                </p>
              </div>
              
              <div className="space-y-3">
                <button
                  onClick={onUpgrade}
                  className="w-full bg-black text-white py-3 font-mono font-bold hover:bg-gray-800 transition-all shadow-[2px_2px_0px_0px_rgba(0,0,0,0.3)]"
                >
                  立即升级 ¥19/月
                </button>
                <button
                  onClick={onTrial}
                  className="w-full bg-white text-black py-3 font-mono font-bold border border-black hover:bg-gray-100 transition-all shadow-[2px_2px_0px_0px_rgba(0,0,0,0.3)]"
                >
                  先试用7天
                </button>
              </div>
              
              <div className="text-center mt-4">
                <p className="text-xs text-gray-500">
                  试用期结束后不会自动扣费
                </p>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  )
}
