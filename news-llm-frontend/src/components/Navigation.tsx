'use client'

import { useSession, signOut } from 'next-auth/react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { useState, useEffect, useRef } from 'react'

export default function Navigation() {
  const { data: session, status } = useSession()
  const pathname = usePathname()
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false)
  const userMenuRef = useRef<HTMLDivElement>(null)

  const navItems = [
    { href: '/news', label: '新闻阅读', icon: '📰' },
    { href: '/subscriptions', label: '发现订阅', icon: '🔍' },
    { href: '/pricing', label: '付费方案', icon: '💎' },
  ]

  // 点击外部关闭用户菜单
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (userMenuRef.current && !userMenuRef.current.contains(event.target as Node)) {
        setIsUserMenuOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  const isActive = (href: string) => {
    if (href === '/') return pathname === '/'
    return pathname.startsWith(href)
  }



  const handleSignOut = async () => {
    await signOut({
      callbackUrl: `${window.location.origin}/auth/signin`,
      redirect: true
    })
  }

  if (status === 'loading') {
    return (
      <nav className="bg-white border-b border-black sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-14">
            <div className="flex items-center">
              <div className="animate-pulse bg-gray-300 h-6 w-32 border border-black"></div>
            </div>
          </div>
        </div>
      </nav>
    )
  }

  if (!session) {
    return (
      <nav className="bg-white border-b-2 border-black sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            {/* Logo */}
            <div className="flex items-center">
              <Link href="/" className="text-2xl font-bold text-black font-mono tracking-tight">
                ® NEWS-LLM
              </Link>
            </div>

            {/* Desktop Navigation */}
            <div className="hidden md:flex items-center space-x-3">
              {navItems.map((item) => (
                <Link
                  key={item.href}
                  href={item.href}
                  className={`flex items-center space-x-2 px-4 py-2 font-mono text-base font-bold transition-all border-2 ${
                    isActive(item.href)
                      ? 'bg-black text-white border-black shadow-[2px_2px_0px_0px_rgba(0,0,0,0.3)]'
                      : 'bg-white text-black border-gray-300 hover:border-black hover:shadow-[2px_2px_0px_0px_rgba(0,0,0,0.1)]'
                  }`}
                >
                  <span className="text-lg">{item.icon}</span>
                  <span>{item.label}</span>
                </Link>
              ))}
            </div>

            {/* Auth buttons */}
            <div className="flex items-center space-x-4">
              <Link
                href="/auth/signin"
                className="text-black hover:text-gray-600 px-4 py-2 font-mono text-sm font-bold border-2 border-gray-300 hover:border-black transition-all"
              >
                SIGN IN
              </Link>
              <Link
                href="/auth/signup"
                className="bg-black text-white px-4 py-2 font-mono text-sm font-bold hover:bg-gray-800 transition-all border-2 border-black shadow-[2px_2px_0px_0px_rgba(0,0,0,0.3)]"
              >
                SIGN UP
              </Link>

              {/* Mobile menu button */}
              <div className="md:hidden">
                <button
                  onClick={() => setIsMenuOpen(!isMenuOpen)}
                  className="text-black hover:text-gray-600 p-2 border-2 border-gray-300 hover:border-black transition-all"
                >
                  <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
                    {isMenuOpen ? (
                      <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
                    ) : (
                      <path strokeLinecap="round" strokeLinejoin="round" d="M4 6h16M4 12h16M4 18h16" />
                    )}
                  </svg>
                </button>
              </div>
            </div>
          </div>

          {/* Mobile Navigation */}
          {isMenuOpen && (
            <div className="md:hidden">
              <div className="px-4 pt-4 pb-4 space-y-3 bg-white border-t-2 border-black">
                {navItems.map((item) => (
                  <Link
                    key={item.href}
                    href={item.href}
                    className={`flex items-center space-x-3 px-4 py-3 font-mono text-lg font-bold border-2 transition-all ${
                      isActive(item.href)
                        ? 'bg-black text-white border-black'
                        : 'bg-white text-black border-gray-300 hover:border-black'
                    }`}
                    onClick={() => setIsMenuOpen(false)}
                  >
                    <span className="text-xl">{item.icon}</span>
                    <span>{item.label}</span>
                  </Link>
                ))}
              </div>
            </div>
          )}
        </div>
      </nav>
    )
  }

  return (
    <nav className="bg-white border-b-2 border-black sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          {/* Logo */}
          <div className="flex items-center">
            <Link href="/" className="text-2xl font-bold text-black font-mono tracking-tight">
              ® NEWS-LLM
            </Link>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-3">
            {navItems.map((item) => (
              <Link
                key={item.href}
                href={item.href}
                className={`flex items-center space-x-2 px-4 py-2 font-mono text-base font-bold transition-all border-2 ${
                  isActive(item.href)
                    ? 'bg-black text-white border-black shadow-[2px_2px_0px_0px_rgba(0,0,0,0.3)]'
                    : 'bg-white text-black border-gray-300 hover:border-black hover:shadow-[2px_2px_0px_0px_rgba(0,0,0,0.1)]'
                }`}
              >
                <span className="text-lg">{item.icon}</span>
                <span>{item.label}</span>
              </Link>
            ))}
          </div>

          {/* User Menu */}
          <div className="flex items-center">
            <div className="hidden md:flex items-center relative">
              <div className="relative" ref={userMenuRef}>
                <button
                  onClick={() => setIsUserMenuOpen(!isUserMenuOpen)}
                  className="flex items-center space-x-2 px-3 py-2 font-mono text-sm font-bold hover:bg-gray-100 transition-all"
                >
                  <div className="text-left">
                    <div className="text-xs text-gray-500 uppercase">USER</div>
                    <div className="font-bold text-sm" title={session.user?.username || session.user?.email}>
                      {session.user?.username || session.user?.email}
                    </div>
                  </div>
                  <svg className={`w-4 h-4 transition-transform ${isUserMenuOpen ? 'rotate-180' : ''}`} fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </button>

                {/* 用户下拉菜单 */}
                {isUserMenuOpen && (
                  <div className="absolute right-0 mt-2 w-48 bg-white border border-black shadow-[3px_3px_0px_0px_rgba(0,0,0,1)] z-50">
                    <div className="py-1">
                      <Link
                        href="/billing"
                        className="flex items-center px-4 py-2 text-sm font-mono font-bold text-black hover:bg-gray-100 transition-all"
                        onClick={() => setIsUserMenuOpen(false)}
                      >
                        <span className="mr-3">📄</span>
                        <span>账单</span>
                      </Link>
                      <Link
                        href="/my-subscriptions"
                        className="flex items-center px-4 py-2 text-sm font-mono font-bold text-black hover:bg-gray-100 transition-all"
                        onClick={() => setIsUserMenuOpen(false)}
                      >
                        <span className="mr-3">📋</span>
                        <span>我的订阅</span>
                      </Link>
                      <div className="border-t border-gray-300 my-1"></div>
                      <Link
                        href="/settings"
                        className="flex items-center px-4 py-2 text-sm font-mono font-bold text-black hover:bg-gray-100 transition-all"
                        onClick={() => setIsUserMenuOpen(false)}
                      >
                        <span className="mr-3">⚙️</span>
                        <span>设置</span>
                      </Link>
                      <div className="border-t border-gray-300 my-1"></div>
                      <button
                        onClick={() => {
                          setIsUserMenuOpen(false)
                          handleSignOut()
                        }}
                        className="w-full text-left flex items-center px-4 py-2 text-sm font-mono font-bold text-black hover:bg-gray-100 transition-all"
                      >
                        <span className="mr-3">🚪</span>
                        <span>退出登录</span>
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Mobile menu button */}
            <div className="md:hidden">
              <button
                onClick={() => setIsMenuOpen(!isMenuOpen)}
                className="text-black hover:text-gray-600 p-2 border-2 border-gray-300 hover:border-black transition-all"
              >
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
                  {isMenuOpen ? (
                    <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
                  ) : (
                    <path strokeLinecap="round" strokeLinejoin="round" d="M4 6h16M4 12h16M4 18h16" />
                  )}
                </svg>
              </button>
            </div>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden">
            <div className="px-4 pt-4 pb-4 space-y-3 bg-white border-t-2 border-black">
              {navItems.map((item) => (
                <Link
                  key={item.href}
                  href={item.href}
                  className={`flex items-center space-x-3 px-4 py-3 font-mono text-lg font-bold border-2 transition-all ${
                    isActive(item.href)
                      ? 'bg-black text-white border-black'
                      : 'bg-white text-black border-gray-300 hover:border-black'
                  }`}
                  onClick={() => setIsMenuOpen(false)}
                >
                  <span className="text-xl">{item.icon}</span>
                  <span>{item.label}</span>
                </Link>
              ))}
              <div className="border-t-2 border-black pt-4 mt-4">
                <div className="px-4 py-2 text-sm font-mono">
                  <div className="text-xs text-gray-600">USER</div>
                  <div className="font-bold text-black">{session.user?.username || session.user?.email}</div>
                </div>
                <Link
                  href="/billing"
                  className="w-full flex items-center px-4 py-3 font-mono text-base font-bold text-black border-2 border-gray-300 hover:border-black hover:bg-gray-50 transition-all mb-2"
                  onClick={() => setIsMenuOpen(false)}
                >
                  <span className="mr-3">📄</span>
                  <span>账单</span>
                </Link>
                <Link
                  href="/my-subscriptions"
                  className="w-full flex items-center px-4 py-3 font-mono text-base font-bold text-black border-2 border-gray-300 hover:border-black hover:bg-gray-50 transition-all mb-2"
                  onClick={() => setIsMenuOpen(false)}
                >
                  <span className="mr-3">📋</span>
                  <span>我的订阅</span>
                </Link>
                <Link
                  href="/settings"
                  className="w-full flex items-center px-4 py-3 font-mono text-base font-bold text-black border-2 border-gray-300 hover:border-black hover:bg-gray-50 transition-all mb-2"
                  onClick={() => setIsMenuOpen(false)}
                >
                  <span className="mr-3">⚙️</span>
                  <span>设置</span>
                </Link>
                <button
                  onClick={() => {
                    setIsMenuOpen(false)
                    handleSignOut()
                  }}
                  className="w-full text-left flex items-center px-4 py-3 font-mono text-base font-bold text-black border-2 border-gray-300 hover:border-black hover:bg-gray-50 transition-all"
                >
                  <span className="mr-3">🚪</span>
                  <span>退出登录</span>
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </nav>
  )
}
