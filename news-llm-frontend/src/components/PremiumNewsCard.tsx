'use client'

import Link from 'next/link'

interface PremiumNewsCardProps {
  id: string
  title: string
  summary: string
  source: string
  publishedAt: string
  aiScore: number
  tags: string[]
  readTime: number
  isSystem: boolean
  isRead?: boolean
  onMarkAsRead?: (id: string) => void
}

export default function PremiumNewsCard({
  id,
  title,
  summary,
  source,
  publishedAt,
  aiScore,
  tags,
  readTime,
  isSystem,
  isRead = false,
  onMarkAsRead
}: PremiumNewsCardProps) {

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInHours = Math.abs(now.getTime() - date.getTime()) / (1000 * 60 * 60)
    
    if (diffInHours < 24) {
      return `${Math.floor(diffInHours)}小时前`
    } else {
      const diffInDays = Math.floor(diffInHours / 24)
      if (diffInDays === 1) return '昨天'
      if (diffInDays < 7) return `${diffInDays}天前`
      return date.toLocaleDateString('zh-CN', { 
        month: 'short', 
        day: 'numeric' 
      })
    }
  }

  const getScoreColor = (score: number) => {
    if (score >= 4.5) return 'bg-green-500'
    if (score >= 4.0) return 'bg-blue-500'
    return 'bg-gray-500'
  }

  const getPremiumBadge = (score: number) => {
    if (score >= 4.5) return { text: 'PREMIUM+', color: 'bg-gradient-to-r from-yellow-400 to-orange-500' }
    if (score >= 4.0) return { text: 'PREMIUM', color: 'bg-gradient-to-r from-blue-500 to-purple-600' }
    return null
  }

  const premiumBadge = getPremiumBadge(aiScore)

  return (
    <article className={`bg-white border border-black shadow-[3px_3px_0px_0px_rgba(0,0,0,1)] hover:shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] transition-all duration-300 relative overflow-hidden ${
      isRead ? 'opacity-60' : ''
    }`}>
      {/* 付费内容标识条 */}
      {premiumBadge && (
        <div className={`${premiumBadge.color} text-white text-center py-1`}>
          <div className="text-xs font-mono font-bold flex items-center justify-center">
            <span className="mr-1">⭐</span>
            <span>{premiumBadge.text} 付费专享内容</span>
            <span className="ml-1">⭐</span>
          </div>
        </div>
      )}

      {/* 票券头部 */}
      <div className="bg-black text-white p-2 flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="bg-white text-black px-2 py-1 font-mono text-xs font-bold">
            #{String(Math.floor(Math.random() * 999) + 1).padStart(3, '0')}
          </div>
          <div className="border-l border-gray-400 pl-3">
            <div className="text-xs font-mono opacity-80">SOURCE</div>
            <div className="text-sm font-bold tracking-wide">
              {source}
              <span className="ml-2 text-xs bg-gray-700 px-1 py-0.5 rounded">
                {isSystem ? '系统' : '订阅'}
              </span>
            </div>
          </div>
        </div>
        <div className="text-right">
          <div className="text-xs font-mono opacity-80">RATING</div>
          <div className="text-lg font-bold flex items-center">
            <span className={`w-3 h-3 rounded-full ${getScoreColor(aiScore)} mr-1`}></span>
            ★ {aiScore}
            {premiumBadge && (
              <span className="ml-2 text-xs bg-yellow-500 text-black px-1 py-0.5 rounded font-mono font-bold">
                PRO
              </span>
            )}
          </div>
        </div>
      </div>

      {/* 票券主体 */}
      <div className="p-3">
        {/* 时间戳和状态 */}
        <div className="flex items-center justify-between mb-3 pb-2 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <div className="text-xs font-mono text-gray-500">
              {formatDate(publishedAt)}
            </div>
            <div className="text-xs font-mono text-gray-500">
              {readTime} MIN READ
            </div>
          </div>
          <div className="flex items-center space-x-2">
            {isRead && (
              <span className="bg-gray-200 text-gray-700 px-2 py-1 text-xs font-mono font-bold">
                READ
              </span>
            )}
            {premiumBadge && (
              <span className="bg-yellow-100 text-yellow-700 px-2 py-1 text-xs font-mono font-bold">
                付费专享
              </span>
            )}
          </div>
        </div>

        {/* 标题区域 */}
        <Link
          href={`/news/${id}`}
          className="block hover:text-gray-600 transition-colors mb-3"
          onClick={() => onMarkAsRead?.(id)}
        >
          <h2 className="text-xl font-bold text-black mb-1 line-clamp-2 leading-tight">
            {title}
          </h2>
          <div className="w-12 h-0.5 bg-black"></div>
        </Link>

        {/* 摘要 - 完全可见 */}
        <p className="text-gray-700 mb-3 line-clamp-2 leading-relaxed text-sm">
          {summary}
        </p>

        {/* 标签区域 */}
        <div className="flex flex-wrap gap-2 mb-3">
          {tags.map((tag, tagIndex) => (
            <span
              key={tag}
              className="px-2 py-1 bg-gray-100 text-black text-xs font-mono font-bold border border-gray-300"
            >
              #{tagIndex + 1} {tag}
            </span>
          ))}
        </div>

        {/* 操作区域 */}
        <div className="border-t border-dashed border-gray-300 pt-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <button
                onClick={(e) => {
                  e.preventDefault()
                  onMarkAsRead?.(id)
                  window.open(`/news/${id}`, '_blank', 'noopener,noreferrer')
                }}
                className="bg-black text-white px-4 py-2 font-mono text-sm font-bold hover:bg-gray-800 transition-all border border-black shadow-[1px_1px_0px_0px_rgba(0,0,0,0.3)] cursor-pointer"
              >
                READ FULL →
              </button>

              <button className="flex items-center space-x-1 text-gray-600 hover:text-black px-3 py-2 border border-gray-300 hover:border-black transition-all font-mono text-sm font-bold">
                <span>↗</span>
                <span>SHARE</span>
              </button>
            </div>
            <div className="text-xs font-mono text-gray-500">
              {readTime} MIN READ
            </div>
          </div>
        </div>
      </div>
    </article>
  )
}
