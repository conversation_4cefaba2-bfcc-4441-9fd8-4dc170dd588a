'use client'

import { useState } from 'react'
import { useSession } from 'next-auth/react'

interface PaymentModalProps {
  isOpen: boolean
  onClose: () => void
  onSuccess?: () => void
  selectedPlan?: PricingPlan
  market?: 'cn' | 'us'
}

interface PricingPlan {
  id: string
  name: string
  period: 'monthly' | 'yearly'
  price: {
    cn: { amount: number; currency: '¥' }
    us: { amount: number; currency: '$' }
  }
  originalPrice?: {
    cn: { amount: number; currency: '¥' }
    us: { amount: number; currency: '$' }
  }
  popular?: boolean
}

const plans: PricingPlan[] = [
  {
    id: 'pro-monthly',
    name: '专业版月付',
    period: 'monthly',
    price: {
      cn: { amount: 19, currency: '¥' },
      us: { amount: 6.99, currency: '$' }
    }
  },
  {
    id: 'pro-yearly',
    name: '专业版年付',
    period: 'yearly',
    price: {
      cn: { amount: 128, currency: '¥' },
      us: { amount: 59, currency: '$' }
    },
    originalPrice: {
      cn: { amount: 228, currency: '¥' },
      us: { amount: 83.88, currency: '$' }
    },
    popular: true
  }
]

export default function PaymentModal({
  isOpen,
  onClose,
  onSuccess,
  selectedPlan = plans[1],
  market = 'cn'
}: PaymentModalProps) {
  const { data: session } = useSession()
  const [paymentMethod, setPaymentMethod] = useState<'wechat' | 'alipay' | 'stripe'>(
    market === 'cn' ? 'wechat' : 'stripe'
  )
  const [isProcessing, setIsProcessing] = useState(false)
  const [step, setStep] = useState<'payment' | 'processing' | 'success'>('payment')

  if (!isOpen) return null



  const handlePayment = async () => {
    if (!session?.user?.id) return

    setIsProcessing(true)
    setStep('processing')

    try {
      // 处理支付
      const orderResponse = await fetch('/api/payment/process', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          planId: selectedPlan.id,
          paymentMethod,
          market
        })
      })

      if (!orderResponse.ok) {
        const errorText = await orderResponse.text()
        console.error('支付API错误:', orderResponse.status, errorText)
        throw new Error(`支付处理失败: ${orderResponse.status}`)
      }

      const result = await orderResponse.json()
      console.log('支付API响应:', result)

      if (result.success) {
        // 支付成功
        setStep('success')
        setTimeout(() => {
          onSuccess?.()
          onClose()
        }, 2000)
      } else {
        throw new Error(result.error || '支付失败')
      }
    } catch (error) {
      console.error('支付失败:', error)
      alert(`支付失败: ${error.message}`)
      setStep('payment')
    } finally {
      setIsProcessing(false)
    }
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white border-2 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,1)] max-w-md w-full max-h-[90vh] overflow-y-auto">
        {/* 头部 */}
        <div className="bg-black text-white p-4 flex items-center justify-between">
          <h3 className="text-lg font-bold font-mono">
            {step === 'payment' && 'PAYMENT'}
            {step === 'processing' && 'PROCESSING'}
            {step === 'success' && 'SUCCESS'}
          </h3>
          <button
            onClick={onClose}
            className="text-white hover:text-gray-300 text-xl"
            disabled={isProcessing}
          >
            ×
          </button>
        </div>



        {/* 支付方式 */}
        {step === 'payment' && (
          <div className="p-6">
            {/* 选中的方案 */}
            <div className="bg-gray-50 border border-gray-200 p-4 mb-6">
              <div className="flex items-center justify-between">
                <div>
                  <div className="font-bold text-black">{selectedPlan.name}</div>
                  <div className="text-sm text-gray-600">
                    {selectedPlan.period === 'yearly' ? '年付方案' : '月付方案'}
                  </div>
                </div>
                <div className="text-2xl font-bold text-black font-mono">
                  {selectedPlan.price[market].currency}{selectedPlan.price[market].amount}
                </div>
              </div>
            </div>

            {/* 支付方式选择 */}
            <div className="mb-6">
              <h4 className="font-bold text-black mb-3">选择支付方式</h4>
              <div className="space-y-2">
                {market === 'cn' ? (
                  <>
                    <label className="flex items-center p-3 border border-gray-200 cursor-pointer hover:bg-gray-50">
                      <input
                        type="radio"
                        name="payment"
                        value="wechat"
                        checked={paymentMethod === 'wechat'}
                        onChange={(e) => setPaymentMethod(e.target.value as 'wechat')}
                        className="mr-3"
                      />
                      <span className="text-2xl mr-3">💚</span>
                      <span className="font-bold">微信支付</span>
                    </label>
                    <label className="flex items-center p-3 border border-gray-200 cursor-pointer hover:bg-gray-50">
                      <input
                        type="radio"
                        name="payment"
                        value="alipay"
                        checked={paymentMethod === 'alipay'}
                        onChange={(e) => setPaymentMethod(e.target.value as 'alipay')}
                        className="mr-3"
                      />
                      <span className="text-2xl mr-3">💙</span>
                      <span className="font-bold">支付宝</span>
                    </label>
                  </>
                ) : (
                  <label className="flex items-center p-3 border border-gray-200 cursor-pointer hover:bg-gray-50">
                    <input
                      type="radio"
                      name="payment"
                      value="stripe"
                      checked={paymentMethod === 'stripe'}
                      onChange={(e) => setPaymentMethod(e.target.value as 'stripe')}
                      className="mr-3"
                    />
                    <span className="text-2xl mr-3">💳</span>
                    <span className="font-bold">信用卡支付</span>
                  </label>
                )}
              </div>
            </div>

            {/* 支付按钮 */}
            <button
              onClick={handlePayment}
              disabled={isProcessing}
              className="w-full bg-black text-white py-3 font-mono font-bold hover:bg-gray-800 transition-all shadow-[2px_2px_0px_0px_rgba(0,0,0,0.3)] disabled:opacity-50"
            >
              立即支付 {selectedPlan.price[market].currency}{selectedPlan.price[market].amount}
            </button>


          </div>
        )}

        {/* 处理中 */}
        {step === 'processing' && (
          <div className="p-8 text-center">
            <div className="text-6xl mb-4">⏳</div>
            <h4 className="text-xl font-bold text-black mb-2">处理支付中...</h4>
            <p className="text-gray-600 mb-4">
              请稍候，正在处理您的支付请求
            </p>
            <div className="w-8 h-8 border-2 border-black border-t-transparent animate-spin mx-auto"></div>
          </div>
        )}

        {/* 成功 */}
        {step === 'success' && (
          <div className="p-8 text-center">
            <div className="text-6xl mb-4">🎉</div>
            <h4 className="text-xl font-bold text-black mb-2">支付成功！</h4>
            <p className="text-gray-600 mb-4">
              恭喜您成功升级到专业版，即将跳转...
            </p>
            <div className="bg-green-50 border border-green-200 p-3 text-sm text-green-800">
              您现在可以享受所有专业版功能了！
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
