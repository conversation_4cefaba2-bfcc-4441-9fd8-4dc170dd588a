'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import Link from 'next/link'

interface LockedContentBannerProps {
  onUpgrade: () => void
  onTrial: () => void
}

export default function LockedContentBanner({ onUpgrade, onTrial }: LockedContentBannerProps) {
  const { data: session } = useSession()
  const [lockedCount, setLockedCount] = useState(0)
  const [isVisible, setIsVisible] = useState(false)
  const [isDismissed, setIsDismissed] = useState(false)

  useEffect(() => {
    const fetchLockedCount = async () => {
      if (!session?.user?.id) return

      try {
        const response = await fetch(`/api/news/locked-count?user_id=${session.user.id}`)
        if (response.ok) {
          const data = await response.json()
          setLockedCount(data.count)
          setIsVisible(data.count > 0)
        }
      } catch (error) {
        console.error('获取锁定内容数量失败:', error)
      }
    }

    fetchLockedCount()
  }, [session])

  if (!isVisible || isDismissed || lockedCount === 0) {
    return null
  }

  return (
    <div className="bg-gradient-to-r from-yellow-400 to-orange-400 border-b-2 border-black shadow-[0_2px_0px_0px_rgba(0,0,0,1)]">
      <div className="max-w-7xl mx-auto px-4 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            {/* 图标和主要信息 */}
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-black text-white rounded-full flex items-center justify-center">
                <span className="text-lg">🔒</span>
              </div>
              <div>
                <div className="font-bold text-black text-lg font-mono">
                  今天有 {lockedCount} 条 ≥4.0★ 高分内容待解锁
                </div>
                <div className="text-sm text-black opacity-80">
                  升级到专业版，立即查看所有重要新闻
                </div>
              </div>
            </div>

            {/* 示例内容预览 */}
            <div className="hidden lg:flex items-center space-x-2 ml-8">
              <div className="text-xs text-black opacity-70">例如:</div>
              <div className="flex space-x-2">
                <div className="bg-black text-white px-2 py-1 text-xs font-mono rounded">
                  ★4.8 OpenAI发布GPT-5
                </div>
                <div className="bg-black text-white px-2 py-1 text-xs font-mono rounded">
                  ★4.5 比特币突破新高
                </div>
                <div className="bg-black text-white px-2 py-1 text-xs font-mono rounded">
                  ★4.2 苹果发布新品
                </div>
              </div>
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="flex items-center space-x-3">
            <button
              onClick={onUpgrade}
              className="bg-black text-white px-6 py-2 font-mono font-bold text-sm hover:bg-gray-800 transition-all shadow-[2px_2px_0px_0px_rgba(0,0,0,0.3)] hover:shadow-[3px_3px_0px_0px_rgba(0,0,0,0.3)]"
            >
              开通专业版
            </button>
            <button
              onClick={onTrial}
              className="bg-white text-black px-6 py-2 font-mono font-bold text-sm border border-black hover:bg-gray-100 transition-all shadow-[2px_2px_0px_0px_rgba(0,0,0,0.3)] hover:shadow-[3px_3px_0px_0px_rgba(0,0,0,0.3)]"
            >
              领取试用
            </button>
            <button
              onClick={() => setIsDismissed(true)}
              className="text-black hover:text-gray-600 text-xl p-1"
              title="关闭提示"
            >
              ×
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

// 简化版横幅（用于移动端）
export function LockedContentBannerMobile({ onUpgrade, onTrial }: LockedContentBannerProps) {
  const { data: session } = useSession()
  const [lockedCount, setLockedCount] = useState(0)
  const [isVisible, setIsVisible] = useState(false)
  const [isDismissed, setIsDismissed] = useState(false)

  useEffect(() => {
    const fetchLockedCount = async () => {
      if (!session?.user?.id) return

      try {
        const response = await fetch(`/api/news/locked-count?user_id=${session.user.id}`)
        if (response.ok) {
          const data = await response.json()
          setLockedCount(data.count)
          setIsVisible(data.count > 0)
        }
      } catch (error) {
        console.error('获取锁定内容数量失败:', error)
      }
    }

    fetchLockedCount()
  }, [session])

  if (!isVisible || isDismissed || lockedCount === 0) {
    return null
  }

  return (
    <div className="bg-gradient-to-r from-yellow-400 to-orange-400 border-b-2 border-black">
      <div className="px-4 py-3">
        <div className="text-center">
          <div className="font-bold text-black text-sm font-mono mb-2">
            🔒 今天有 {lockedCount} 条高分内容待解锁
          </div>
          <div className="flex space-x-2 justify-center">
            <button
              onClick={onUpgrade}
              className="bg-black text-white px-4 py-1 font-mono font-bold text-xs"
            >
              开通专业版
            </button>
            <button
              onClick={onTrial}
              className="bg-white text-black px-4 py-1 font-mono font-bold text-xs border border-black"
            >
              试用7天
            </button>
          </div>
        </div>
        <button
          onClick={() => setIsDismissed(true)}
          className="absolute top-2 right-2 text-black hover:text-gray-600 text-lg"
        >
          ×
        </button>
      </div>
    </div>
  )
}
