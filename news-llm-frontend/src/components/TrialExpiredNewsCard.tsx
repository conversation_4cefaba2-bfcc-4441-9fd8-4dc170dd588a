'use client'

import { useState } from 'react'

interface TrialExpiredNewsCardProps {
  id: string
  title: string
  summary: string
  source: string
  publishedAt: string
  aiScore: number
  tags: string[]
  readTime: number
  isSystem: boolean
  onUpgrade: () => void
}

export default function TrialExpiredNewsCard({
  id,
  title,
  summary,
  source,
  publishedAt,
  aiScore,
  tags,
  readTime,
  isSystem,
  onUpgrade
}: TrialExpiredNewsCardProps) {
  const [showPaymentModal, setShowPaymentModal] = useState(false)

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInHours = Math.abs(now.getTime() - date.getTime()) / (1000 * 60 * 60)
    
    if (diffInHours < 24) {
      return `${Math.floor(diffInHours)}小时前`
    } else {
      const diffInDays = Math.floor(diffInHours / 24)
      if (diffInDays === 1) return '昨天'
      if (diffInDays < 7) return `${diffInDays}天前`
      return date.toLocaleDateString('zh-CN', { 
        month: 'short', 
        day: 'numeric' 
      })
    }
  }

  const getScoreColor = (score: number) => {
    if (score >= 4.5) return 'bg-green-500'
    if (score >= 4.0) return 'bg-blue-500'
    return 'bg-gray-500'
  }

  // 截断摘要显示（试用过期显示前20字）
  const visibleSummary = summary.length > 20 ? summary.substring(0, 20) : summary
  const hiddenSummary = summary.length > 20 ? summary.substring(20) : ''

  return (
    <>
      <article className="bg-white border border-black shadow-[3px_3px_0px_0px_rgba(0,0,0,1)] hover:shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] transition-all duration-300 relative overflow-hidden opacity-75">
        {/* 票券头部 - 试用过期状态 */}
        <div className="bg-black text-white p-2 flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="bg-red-500 text-white px-2 py-1 font-mono text-xs font-bold">
              🔒 试用已过期
            </div>
            <div className="border-l border-gray-400 pl-3">
              <div className="text-xs font-mono opacity-80">SOURCE</div>
              <div className="text-sm font-bold tracking-wide flex items-center">
                {source}
                <span className="ml-2 text-xs bg-gray-700 px-1 py-0.5 rounded">
                  {isSystem ? '系统' : '订阅'}
                </span>
              </div>
            </div>
          </div>
          <div className="text-right">
            <div className="text-xs font-mono opacity-80">RATING</div>
            <div className="text-lg font-bold flex items-center">
              <span className={`w-3 h-3 rounded-full ${getScoreColor(aiScore)} mr-1`}></span>
              ★ {aiScore}
            </div>
          </div>
        </div>

        {/* 票券主体 */}
        <div className="p-3">
          {/* 时间戳 */}
          <div className="flex items-center justify-between mb-3 pb-2 border-b border-gray-200">
            <div className="text-xs font-mono text-gray-500">
              {formatDate(publishedAt)}
            </div>
            <div className="flex items-center space-x-2">
              <span className="bg-red-100 text-red-700 px-2 py-1 text-xs font-mono font-bold">
                试用已过期
              </span>
            </div>
          </div>

          {/* 标题区域 */}
          <div className="mb-3">
            <h2 className="text-xl font-bold text-black mb-1 line-clamp-2 leading-tight">
              {title}
            </h2>
            <div className="w-12 h-0.5 bg-black"></div>
          </div>

          {/* 摘要 - 更严格的蒙层效果 */}
          <div className="relative mb-3">
            <p className="text-gray-700 mb-3 line-clamp-2 leading-relaxed text-sm">
              <span>{visibleSummary}</span>
              {hiddenSummary && (
                <span className="relative inline-block">
                  <span className="text-gray-200 blur-[2px] select-none opacity-40">
                    {hiddenSummary.substring(0, 10)}
                  </span>
                  <span className="absolute inset-0 bg-gradient-to-r from-transparent via-white/70 to-white pointer-events-none"></span>
                  <span className="ml-1 text-xs text-red-500 font-mono">[试用已过期]</span>
                </span>
              )}
            </p>
          </div>

          {/* 标签区域 */}
          <div className="flex flex-wrap gap-2 mb-3">
            {tags.slice(0, 2).map((tag, tagIndex) => (
              <span
                key={tag}
                className="px-2 py-1 bg-gray-100 text-black text-xs font-mono font-bold border border-gray-300 opacity-60"
              >
                #{tagIndex + 1} {tag}
              </span>
            ))}
            {tags.length > 2 && (
              <span className="px-2 py-1 bg-gray-100 text-gray-500 text-xs font-mono font-bold border border-gray-300 opacity-60">
                +{tags.length - 2}
              </span>
            )}
          </div>

          {/* 操作区域 - 只显示升级选项 */}
          <div className="border-t border-dashed border-gray-300 pt-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <button
                  onClick={() => setShowPaymentModal(true)}
                  className="bg-red-600 text-white px-4 py-2 font-mono text-sm font-bold hover:bg-red-700 transition-all border border-red-600 shadow-[1px_1px_0px_0px_rgba(0,0,0,0.3)]"
                >
                  立即续费 ¥19/月 →
                </button>

                <div className="text-xs text-gray-500 font-mono">
                  试用已结束，需要付费继续使用
                </div>
              </div>
              <div className="text-xs font-mono text-gray-500">
                {readTime} MIN READ
              </div>
            </div>
          </div>
        </div>
      </article>

      {/* 支付弹窗 */}
      {showPaymentModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white border-2 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,1)] max-w-md w-full">
            <div className="bg-red-600 text-white p-4 flex items-center justify-between">
              <h3 className="text-lg font-bold font-mono">试用已过期</h3>
              <button
                onClick={() => setShowPaymentModal(false)}
                className="text-white hover:text-gray-300 text-xl"
              >
                ×
              </button>
            </div>
            <div className="p-6">
              <div className="text-center mb-6">
                <div className="text-4xl mb-2">⏰</div>
                <h4 className="text-xl font-bold text-black mb-2">
                  7天试用已结束
                </h4>
                <p className="text-gray-600 text-sm">
                  继续订阅专业版，解锁所有≥4.0★高分内容
                </p>
              </div>
              
              <div className="space-y-3">
                <button
                  onClick={onUpgrade}
                  className="w-full bg-red-600 text-white py-3 font-mono font-bold hover:bg-red-700 transition-all shadow-[2px_2px_0px_0px_rgba(0,0,0,0.3)]"
                >
                  立即续费 ¥19/月
                </button>
                <button
                  onClick={() => setShowPaymentModal(false)}
                  className="w-full bg-white text-black py-3 font-mono font-bold border border-black hover:bg-gray-100 transition-all shadow-[2px_2px_0px_0px_rgba(0,0,0,0.3)]"
                >
                  稍后再说
                </button>
              </div>
              
              <div className="text-center mt-4">
                <p className="text-xs text-gray-500">
                  续费后立即恢复所有高分内容访问权限
                </p>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  )
}
