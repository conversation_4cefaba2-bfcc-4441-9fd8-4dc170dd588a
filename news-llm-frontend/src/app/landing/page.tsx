'use client'

import Link from 'next/link'
import { useSession } from 'next-auth/react'
import { useState, useEffect } from 'react'
import Navigation from '@/components/Navigation'

export default function LandingPage() {
  const { data: session } = useSession()
  const [currentTime, setCurrentTime] = useState('')
  const [lostTime, setLostTime] = useState(0)

  useEffect(() => {
    const updateTime = () => {
      const now = new Date()
      setCurrentTime(now.toLocaleTimeString('zh-CN'))
      // 模拟用户每天浪费在无效信息上的时间（分钟）
      setLostTime(Math.floor((now.getHours() * 60 + now.getMinutes()) * 0.3))
    }

    updateTime()
    const timer = setInterval(updateTime, 1000)
    return () => clearInterval(timer)
  }, [])

  return (
    <div className="min-h-screen bg-gradient-to-br from-red-50 via-orange-50 to-yellow-50">
      <Navigation />

      {/* 紧迫感横幅 */}
      <div className="bg-red-600 text-white py-2 px-4 text-center font-mono text-sm animate-pulse">
        ⚠️ 现在 {currentTime} - 您今天已浪费 {lostTime} 分钟在无效信息上 ⚠️
      </div>

      {/* Hero Section - 痛点驱动 */}
      <section className="py-16 px-4">
        <div className="max-w-6xl mx-auto">
          {/* 主标题 - 恐惧驱动 */}
          <div className="text-center mb-16">
            <div className="bg-black text-white p-8 border-4 border-red-500 shadow-[8px_8px_0px_0px_rgba(239,68,68,1)] mb-8">
              <h1 className="text-4xl md:text-6xl font-bold font-mono mb-4 text-red-400">
                停止被算法操控！
              </h1>
              <p className="text-xl md:text-2xl text-white mb-4">
                每天被推荐算法喂养垃圾信息？
              </p>
              <p className="text-lg text-red-300">
                是时候夺回信息主导权了
              </p>
            </div>
          </div>

          {/* 痛点展示 */}
          <div className="grid md:grid-cols-3 gap-6 mb-16">
            <div className="bg-white border-2 border-black p-6 shadow-[4px_4px_0px_0px_rgba(0,0,0,1)]">
              <div className="text-4xl mb-4">😵‍💫</div>
              <h3 className="text-xl font-bold mb-2 text-red-600">信息过载</h3>
              <p className="text-gray-700">每天被无关紧要的内容轰炸，真正重要的信息却被埋没</p>
            </div>
            <div className="bg-white border-2 border-black p-6 shadow-[4px_4px_0px_0px_rgba(0,0,0,1)]">
              <div className="text-4xl mb-4">🎯</div>
              <h3 className="text-xl font-bold mb-2 text-red-600">算法偏见</h3>
              <p className="text-gray-700">推荐算法只给你想看的，让你活在信息茧房里</p>
            </div>
            <div className="bg-white border-2 border-black p-6 shadow-[4px_4px_0px_0px_rgba(0,0,0,1)]">
              <div className="text-4xl mb-4">⏰</div>
              <h3 className="text-xl font-bold mb-2 text-red-600">时间浪费</h3>
              <p className="text-gray-700">花费大量时间筛选信息，效率极低</p>
            </div>
          </div>

          {/* 解决方案 - 价值主张 */}
          <div className="bg-gradient-to-r from-green-400 to-blue-500 text-white p-8 border-4 border-black shadow-[8px_8px_0px_0px_rgba(0,0,0,1)] mb-16">
            <div className="text-center">
              <h2 className="text-3xl md:text-5xl font-bold font-mono mb-6">
                NEWS-LLM
              </h2>
              <p className="text-xl md:text-2xl mb-4">
                🤖 AI驱动的智能新闻平台
              </p>
              <p className="text-lg mb-6">
                打破推荐算法的信息茧房，让AI为你精准筛选真正有价值的内容
              </p>

              {/* 核心卖点 */}
              <div className="grid md:grid-cols-2 gap-4 text-left">
                <div className="bg-white/20 p-4 rounded border border-white/30">
                  <h4 className="font-bold mb-2">🎯 GPT智能筛选</h4>
                  <p className="text-sm">AI自动过滤低质量内容，只推送高价值信息</p>
                </div>
                <div className="bg-white/20 p-4 rounded border border-white/30">
                  <h4 className="font-bold mb-2">📝 AI生成摘要</h4>
                  <p className="text-sm">秒懂核心要点，节省80%阅读时间</p>
                </div>
                <div className="bg-white/20 p-4 rounded border border-white/30">
                  <h4 className="font-bold mb-2">🔒 订阅制模式</h4>
                  <p className="text-sm">无广告干扰，纯净阅读体验</p>
                </div>
                <div className="bg-white/20 p-4 rounded border border-white/30">
                  <h4 className="font-bold mb-2">⚡ 高效筛选</h4>
                  <p className="text-sm">自定义评分标准，精准匹配你的需求</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 社会证明 + 稀缺性 */}
      <section className="py-16 px-4 bg-black text-white">
        <div className="max-w-6xl mx-auto text-center">
          <div className="mb-12">
            <h2 className="text-3xl md:text-4xl font-bold font-mono mb-4 text-yellow-400">
              🔥 限时内测中 🔥
            </h2>
            <p className="text-xl mb-4">
              已有 <span className="text-yellow-400 font-bold">2,847</span> 位精英用户抢先体验
            </p>
            <p className="text-red-400 font-bold animate-pulse">
              ⚠️ 仅剩 153 个免费名额
            </p>
          </div>

          {/* 用户证言 */}
          <div className="grid md:grid-cols-3 gap-6 mb-12">
            <div className="bg-white/10 p-6 border border-white/20 rounded">
              <div className="text-yellow-400 mb-2">⭐⭐⭐⭐⭐</div>
              <p className="mb-4">"终于不用被算法绑架了！每天节省2小时筛选时间"</p>
              <p className="text-sm text-gray-300">- 张总，投资人</p>
            </div>
            <div className="bg-white/10 p-6 border border-white/20 rounded">
              <div className="text-yellow-400 mb-2">⭐⭐⭐⭐⭐</div>
              <p className="mb-4">"AI摘要太准了，3秒看懂核心要点，效率提升10倍"</p>
              <p className="text-sm text-gray-300">- 李经理，科技公司</p>
            </div>
            <div className="bg-white/10 p-6 border border-white/20 rounded">
              <div className="text-yellow-400 mb-2">⭐⭐⭐⭐⭐</div>
              <p className="mb-4">"再也不会错过重要信息了，这就是未来的新闻阅读方式"</p>
              <p className="text-sm text-gray-300">- 王博士，研究员</p>
            </div>
          </div>
        </div>
      </section>

      {/* 损失厌恶 + 对比 */}
      <section className="py-16 px-4">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-black mb-4 font-mono">
              你还在用传统方式获取信息吗？
            </h2>
          </div>

          <div className="grid md:grid-cols-2 gap-8">
            {/* 传统方式 - 痛苦 */}
            <div className="bg-red-50 border-2 border-red-500 p-8 shadow-[4px_4px_0px_0px_rgba(239,68,68,1)]">
              <h3 className="text-2xl font-bold text-red-600 mb-6 text-center">😫 传统新闻阅读</h3>
              <div className="space-y-4">
                <div className="flex items-center">
                  <span className="text-red-500 mr-3">❌</span>
                  <span>被算法操控，信息茧房</span>
                </div>
                <div className="flex items-center">
                  <span className="text-red-500 mr-3">❌</span>
                  <span>广告满天飞，干扰阅读</span>
                </div>
                <div className="flex items-center">
                  <span className="text-red-500 mr-3">❌</span>
                  <span>信息质量参差不齐</span>
                </div>
                <div className="flex items-center">
                  <span className="text-red-500 mr-3">❌</span>
                  <span>需要大量时间筛选</span>
                </div>
                <div className="flex items-center">
                  <span className="text-red-500 mr-3">❌</span>
                  <span>错过重要信息</span>
                </div>
              </div>
              <div className="mt-6 p-4 bg-red-100 border border-red-300 rounded">
                <p className="text-red-700 font-bold text-center">
                  💸 每天浪费 2+ 小时 = 年损失 730 小时
                </p>
              </div>
            </div>

            {/* NEWS-LLM - 快乐 */}
            <div className="bg-green-50 border-2 border-green-500 p-8 shadow-[4px_4px_0px_0px_rgba(34,197,94,1)]">
              <h3 className="text-2xl font-bold text-green-600 mb-6 text-center">🚀 NEWS-LLM 体验</h3>
              <div className="space-y-4">
                <div className="flex items-center">
                  <span className="text-green-500 mr-3">✅</span>
                  <span>AI智能筛选，打破茧房</span>
                </div>
                <div className="flex items-center">
                  <span className="text-green-500 mr-3">✅</span>
                  <span>纯净阅读，零广告干扰</span>
                </div>
                <div className="flex items-center">
                  <span className="text-green-500 mr-3">✅</span>
                  <span>高质量内容，AI评分保证</span>
                </div>
                <div className="flex items-center">
                  <span className="text-green-500 mr-3">✅</span>
                  <span>AI摘要，3秒看懂要点</span>
                </div>
                <div className="flex items-center">
                  <span className="text-green-500 mr-3">✅</span>
                  <span>个性化订阅，不错过重要信息</span>
                </div>
              </div>
              <div className="mt-6 p-4 bg-green-100 border border-green-300 rounded">
                <p className="text-green-700 font-bold text-center">
                  💰 每天节省 2+ 小时 = 年节省 730 小时
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 价值锚定 + 紧迫感 */}
      <section className="py-16 px-4 bg-gradient-to-r from-purple-600 to-blue-600 text-white">
        <div className="max-w-6xl mx-auto text-center">
          <div className="mb-12">
            <h2 className="text-3xl md:text-4xl font-bold font-mono mb-4">
              💎 限时特惠：抢占先机
            </h2>
            <p className="text-xl mb-4">
              内测期间享受超值优惠，错过就是永远错过！
            </p>
          </div>

          {/* 价值对比 */}
          <div className="bg-white/10 p-8 rounded-lg border border-white/20 mb-12">
            <h3 className="text-2xl font-bold mb-6">💰 算一笔账：你的时间值多少钱？</h3>
            <div className="grid md:grid-cols-3 gap-6 text-center">
              <div>
                <div className="text-3xl font-bold text-yellow-400">2小时/天</div>
                <p className="text-sm">传统方式筛选信息</p>
              </div>
              <div>
                <div className="text-3xl font-bold text-yellow-400">730小时/年</div>
                <p className="text-sm">年度时间浪费</p>
              </div>
              <div>
                <div className="text-3xl font-bold text-yellow-400">¥36,500</div>
                <p className="text-sm">按时薪50元计算的损失</p>
              </div>
            </div>
            <div className="mt-6 p-4 bg-red-500/20 border border-red-400 rounded">
              <p className="text-xl font-bold">
                NEWS-LLM 年费仅 ¥128，帮你节省 ¥36,500 的时间成本
              </p>
              <p className="text-lg text-yellow-300">
                投资回报率：28,500% 🚀
              </p>
            </div>
          </div>

          {/* 定价方案 */}
          <div className="grid md:grid-cols-3 gap-8">
            {/* 免费体验 */}
            <div className="bg-white text-black p-8 border-2 border-gray-300 shadow-[4px_4px_0px_0px_rgba(0,0,0,1)]">
              <h3 className="text-xl font-bold mb-4">🎯 免费体验</h3>
              <div className="text-3xl font-bold mb-6">¥0</div>
              <ul className="text-left space-y-2 mb-6 text-sm">
                <li>✅ 基础AI筛选（3.9★以下）</li>
                <li>✅ 分类浏览</li>
                <li>✅ 基础摘要</li>
                <li>❌ 高分内容锁定</li>
                <li>❌ 仅限10个订阅源</li>
              </ul>
              {!session && (
                <Link
                  href="/auth/signup"
                  className="w-full bg-gray-200 text-black px-6 py-3 font-mono font-bold hover:bg-gray-300 transition-all border-2 border-black inline-block"
                >
                  立即免费体验
                </Link>
              )}
            </div>

            {/* 专业版 - 主推 */}
            <div className="bg-gradient-to-b from-yellow-400 to-orange-500 text-black p-8 border-4 border-red-500 shadow-[8px_8px_0px_0px_rgba(239,68,68,1)] relative transform scale-105">
              <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                <span className="bg-red-600 text-white px-6 py-2 font-mono font-bold text-sm border-2 border-black animate-pulse">
                  🔥 最受欢迎
                </span>
              </div>
              <h3 className="text-2xl font-bold mb-4">💎 专业版</h3>
              <div className="mb-4">
                <div className="text-sm line-through text-gray-600">原价 ¥38/月</div>
                <div className="text-4xl font-bold">¥19<span className="text-lg">/月</span></div>
                <div className="text-red-600 font-bold">限时5折！</div>
              </div>
              <ul className="text-left space-y-2 mb-6 text-sm">
                <li>✅ 全部免费功能</li>
                <li>✅ 高级AI筛选（5.0★）</li>
                <li>✅ 高分内容即时解锁</li>
                <li>✅ 100个订阅源配额</li>
                <li>✅ 优先客服支持</li>
              </ul>
              <Link
                href="/auth/signup"
                className="w-full bg-black text-white px-6 py-4 font-mono font-bold hover:bg-gray-800 transition-all border-2 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,0.3)] inline-block"
              >
                🚀 立即升级专业版
              </Link>
              <p className="text-xs mt-2 text-red-700">
                ⏰ 优惠仅剩 {Math.floor(Math.random() * 48 + 12)} 小时
              </p>
            </div>

            {/* 年度版 */}
            <div className="bg-white text-black p-8 border-2 border-green-500 shadow-[4px_4px_0px_0px_rgba(34,197,94,1)]">
              <div className="bg-green-500 text-white px-3 py-1 text-xs font-bold mb-4 inline-block">
                💰 最划算
              </div>
              <h3 className="text-xl font-bold mb-4">🏆 年度版</h3>
              <div className="mb-4">
                <div className="text-sm line-through text-gray-600">¥256/年</div>
                <div className="text-3xl font-bold">¥128<span className="text-lg">/年</span></div>
                <div className="text-green-600 font-bold">省¥128！</div>
              </div>
              <ul className="text-left space-y-2 mb-6 text-sm">
                <li>✅ 全部专业版功能</li>
                <li>✅ 相当于¥10.7/月</li>
                <li>✅ 一次付费全年无忧</li>
                <li>✅ 专属VIP标识</li>
                <li>✅ 新功能优先体验</li>
              </ul>
              <Link
                href="/auth/signup"
                className="w-full bg-green-600 text-white px-6 py-3 font-mono font-bold hover:bg-green-700 transition-all border-2 border-black inline-block"
              >
                💎 抢购年度版
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* 最后冲刺 - FOMO + 行动召唤 */}
      <section className="py-20 px-4 bg-gradient-to-r from-red-600 to-pink-600 text-white">
        <div className="max-w-4xl mx-auto text-center">
          <div className="mb-12">
            <h2 className="text-4xl md:text-5xl font-bold mb-6 font-mono animate-pulse">
              ⚡ 最后机会！⚡
            </h2>
            <p className="text-2xl mb-4">
              内测名额即将用完，错过就要等下一轮
            </p>
            <div className="bg-black/30 p-6 rounded-lg border border-white/20 mb-8">
              <div className="grid md:grid-cols-3 gap-4 text-center">
                <div>
                  <div className="text-3xl font-bold text-yellow-400">153</div>
                  <p className="text-sm">剩余名额</p>
                </div>
                <div>
                  <div className="text-3xl font-bold text-yellow-400">2,847</div>
                  <p className="text-sm">已注册用户</p>
                </div>
                <div>
                  <div className="text-3xl font-bold text-yellow-400">4.9★</div>
                  <p className="text-sm">用户评分</p>
                </div>
              </div>
            </div>
          </div>

          {/* 双重行动召唤 */}
          <div className="space-y-6">
            {session ? (
              <div className="space-y-4">
                <Link
                  href="/news"
                  className="bg-white text-black px-12 py-6 font-mono font-bold text-2xl hover:bg-gray-100 transition-all border-4 border-black shadow-[8px_8px_0px_0px_rgba(0,0,0,1)] inline-block"
                >
                  🚀 立即开始阅读
                </Link>
                <p className="text-lg">
                  您已登录，马上体验AI新闻筛选的魅力！
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                <div className="flex flex-col md:flex-row gap-4 justify-center">
                  <Link
                    href="/auth/signup"
                    className="bg-yellow-400 text-black px-10 py-6 font-mono font-bold text-xl hover:bg-yellow-300 transition-all border-4 border-black shadow-[8px_8px_0px_0px_rgba(0,0,0,1)]"
                  >
                    🎯 免费注册抢名额
                  </Link>
                  <Link
                    href="/news"
                    className="bg-white text-black px-10 py-6 font-mono font-bold text-xl hover:bg-gray-100 transition-all border-4 border-black shadow-[8px_8px_0px_0px_rgba(0,0,0,1)]"
                  >
                    👀 先看看再说
                  </Link>
                </div>
                <p className="text-lg">
                  注册即送7天专业版试用，无需信用卡
                </p>
              </div>
            )}
          </div>

          {/* 风险消除 */}
          <div className="mt-12 bg-white/10 p-6 rounded-lg border border-white/20">
            <h3 className="text-xl font-bold mb-4">🛡️ 零风险承诺</h3>
            <div className="grid md:grid-cols-3 gap-4 text-sm">
              <div>
                <div className="text-2xl mb-2">💰</div>
                <p>7天无理由退款</p>
              </div>
              <div>
                <div className="text-2xl mb-2">🔒</div>
                <p>数据安全保护</p>
              </div>
              <div>
                <div className="text-2xl mb-2">📞</div>
                <p>24/7客服支持</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-black text-white py-8 px-4">
        <div className="max-w-6xl mx-auto text-center">
          <p className="font-mono text-sm opacity-70">
            © 2024 NEWS-LLM. 让AI为你筛选真正有价值的信息。
          </p>
          <p className="text-xs opacity-50 mt-2">
            不要让算法决定你看到什么，让AI帮你看到最重要的。
          </p>
        </div>
      </footer>
    </div>
  )
}
