import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const userId = parseInt(session.user.id)

    // 查询用户的订单记录（根据实际表结构）
    const ordersQuery = `
      SELECT
        o.id,
        o.status,
        o.amount_cents,
        o.currency,
        o.provider as payment_method,
        o.created_at,
        p.name as plan_name,
        p.period
      FROM orders o
      LEFT JOIN plans p ON o.plan_id = p.id
      WHERE o.user_id = $1
      ORDER BY o.created_at DESC
    `

    let orders: any[] = []
    
    try {
      const orderResult = await prisma.$queryRawUnsafe(ordersQuery, userId)
      orders = orderResult as any[]
    } catch (error) {
      console.error('查询订单记录失败:', error)
      // 如果查询失败，返回空数组
      orders = []
    }

    // 转换数据格式
    const billingRecords = orders.map((order, index) => ({
      id: order.id.toString(),
      orderId: `ORDER_${order.id}_${Date.now()}`, // 生成订单号
      planName: order.plan_name || '未知套餐',
      amount: parseInt(order.amount_cents || '0'),
      currency: order.currency || 'CNY',
      status: order.status === 'paid' ? 'completed' : order.status || 'pending',
      paymentMethod: getPaymentMethodName(order.payment_method) || '未知',
      createdAt: order.created_at,
      paidAt: order.status === 'paid' ? order.created_at : null, // 如果已支付，使用创建时间作为支付时间
      description: `${order.plan_name || '未知套餐'}订阅`
    }))

    // 获取支付方式显示名称的辅助函数
    function getPaymentMethodName(method: string): string {
      const methodNames: Record<string, string> = {
        'wechat': '微信支付',
        'alipay': '支付宝',
        'stripe': '信用卡支付'
      }
      return methodNames[method] || method
    }

    return NextResponse.json({
      success: true,
      records: billingRecords
    })

  } catch (error) {
    console.error('获取账单记录失败:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
