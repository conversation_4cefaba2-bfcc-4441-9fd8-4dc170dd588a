import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 获取用户个人信息
export async function GET(request: NextRequest) {
  try {
    // 验证用户认证
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const userId = parseInt(session.user.id)

    // 获取用户信息
    const user = await prisma.users.findUnique({
      where: { id: userId },
      select: {
        id: true,
        username: true,
        email: true,
        language: true,
        created_at: true,
        updated_at: true
      }
    })

    if (!user) {
      return NextResponse.json({ error: '用户不存在' }, { status: 404 })
    }

    // 转换语言格式为前端格式
    const frontendLanguage = user.language === 'en_US' ? 'en' : 'zh'

    return NextResponse.json({
      success: true,
      data: {
        id: user.id,
        username: user.username,
        email: user.email,
        language: frontendLanguage,
        createdAt: user.created_at,
        updatedAt: user.updated_at
      }
    })
  } catch (error) {
    console.error('获取用户信息失败:', error)
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 })
  }
}

// 更新用户个人信息
export async function PUT(request: NextRequest) {
  try {
    // 验证用户认证
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const userId = parseInt(session.user.id)
    const body = await request.json()
    const { username, email } = body

    if (!username || !email) {
      return NextResponse.json({ error: '用户名和邮箱不能为空' }, { status: 400 })
    }

    // 验证邮箱格式
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      return NextResponse.json({ error: '邮箱格式不正确' }, { status: 400 })
    }

    // 检查用户名是否已被其他用户使用
    const existingUserByUsername = await prisma.users.findFirst({
      where: {
        username: username,
        id: { not: userId }
      }
    })

    if (existingUserByUsername) {
      return NextResponse.json({ error: '用户名已被使用' }, { status: 409 })
    }

    // 检查邮箱是否已被其他用户使用
    const existingUserByEmail = await prisma.users.findFirst({
      where: {
        email: email,
        id: { not: userId }
      }
    })

    if (existingUserByEmail) {
      return NextResponse.json({ error: '邮箱已被使用' }, { status: 409 })
    }

    // 更新用户信息
    const updatedUser = await prisma.users.update({
      where: { id: userId },
      data: {
        username: username,
        email: email,
        updated_at: new Date()
      },
      select: {
        id: true,
        username: true,
        email: true,
        language: true,
        updated_at: true
      }
    })

    // 转换语言格式为前端格式
    const frontendLanguage = updatedUser.language === 'en_US' ? 'en' : 'zh'

    return NextResponse.json({
      success: true,
      message: '个人信息更新成功',
      data: {
        id: updatedUser.id,
        username: updatedUser.username,
        email: updatedUser.email,
        language: frontendLanguage,
        updatedAt: updatedUser.updated_at
      }
    })
  } catch (error) {
    console.error('更新用户信息失败:', error)
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 })
  }
}
