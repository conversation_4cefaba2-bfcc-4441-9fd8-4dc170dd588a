import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function POST(request: NextRequest) {
  try {
    // 验证用户认证
    const session = await getServerSession(authOptions)

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { user_id } = await request.json()

    // 验证用户ID
    if (!user_id || parseInt(user_id) !== parseInt(session.user.id)) {
      return NextResponse.json({ error: '无效的用户ID' }, { status: 400 })
    }

    const userId = parseInt(user_id)

    // 确保user_subscriptions表存在
    await prisma.$executeRaw`
      CREATE TABLE IF NOT EXISTS user_subscriptions (
        id SERIAL PRIMARY KEY,
        user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        plan_id INTEGER,
        status VARCHAR(20) NOT NULL DEFAULT 'free',
        plan_name VARCHAR(50),
        trial_availed BOOLEAN NOT NULL DEFAULT false,
        trial_start_at TIMESTAMPTZ,
        trial_end_at TIMESTAMPTZ,
        current_period_start_at TIMESTAMPTZ,
        current_period_end_at TIMESTAMPTZ,
        auto_renew BOOLEAN NOT NULL DEFAULT false,
        created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
        updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
        UNIQUE(user_id)
      )
    `

    // 计算试用结束时间（7天后）
    const trialEndAt = new Date()
    trialEndAt.setDate(trialEndAt.getDate() + 7)

    // 先查找是否已存在该用户的订阅记录
    const existingSubscription = await prisma.user_subscriptions.findFirst({
      where: {
        user_id: userId
      }
    })

    let result
    if (existingSubscription) {
      // 如果存在，更新记录
      result = await prisma.user_subscriptions.update({
        where: {
          id: existingSubscription.id
        },
        data: {
          status: 'trialing',
          plan_name: 'Pro Trial',
          trial_start_at: new Date(),
          trial_end_at: trialEndAt,
          updated_at: new Date()
        }
      })
    } else {
      // 如果不存在，创建新记录
      result = await prisma.user_subscriptions.create({
        data: {
          user_id: userId,
          status: 'trialing',
          plan_name: 'Pro Trial',
          trial_start_at: new Date(),
          trial_end_at: trialEndAt,
          created_at: new Date(),
          updated_at: new Date()
        }
      })
    }



    return NextResponse.json({
      success: true,
      message: '试用已成功开通',
      trial_end_at: trialEndAt
    })

  } catch (error) {
    return NextResponse.json(
      { error: '领取试用失败，请稍后重试' },
      { status: 500 }
    )
  }
}
