import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

interface CheckoutRequest {
  user_id: string
  plan_id: string
  market: 'cn' | 'us'
  payment_method: 'wechat' | 'alipay' | 'stripe'
}

// 预定义的付费计划
const PLANS = {
  'pro-monthly': {
    name: 'Pro Monthly',
    period: 'monthly',
    prices: {
      cn: { amount: 1900, currency: 'CNY' }, // 以分为单位
      us: { amount: 699, currency: 'USD' }   // 以美分为单位
    }
  },
  'pro-yearly': {
    name: 'Pro Yearly',
    period: 'yearly',
    prices: {
      cn: { amount: 12800, currency: 'CNY' },
      us: { amount: 5900, currency: 'USD' }
    }
  }
}

export async function POST(request: NextRequest) {
  try {
    // 验证用户认证
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body: CheckoutRequest = await request.json()
    const { user_id, plan_id, market, payment_method } = body

    // 验证用户ID
    if (parseInt(user_id) !== parseInt(session.user.id)) {
      return NextResponse.json({ error: '无权操作' }, { status: 403 })
    }

    // 验证计划ID
    if (!PLANS[plan_id as keyof typeof PLANS]) {
      return NextResponse.json({ error: '无效的计划ID' }, { status: 400 })
    }

    const plan = PLANS[plan_id as keyof typeof PLANS]
    const price = plan.prices[market]

    // 检查用户当前订阅状态
    const existingSubscription = await prisma.$queryRaw`
      SELECT status, trial_end_at, current_period_end_at
      FROM user_subscriptions 
      WHERE user_id = ${parseInt(user_id)}
      ORDER BY created_at DESC 
      LIMIT 1
    ` as any[]

    // 如果用户已经是付费用户，不允许重复购买
    if (existingSubscription.length > 0 && existingSubscription[0].status === 'active') {
      return NextResponse.json(
        { error: '您已经是付费用户，无需重复购买' },
        { status: 400 }
      )
    }

    // 创建订单记录
    const orderResult = await prisma.$queryRaw`
      INSERT INTO orders (
        user_id,
        plan_id,
        amount_cents,
        currency,
        provider,
        status,
        created_at
      ) VALUES (
        ${parseInt(user_id)},
        ${plan_id},
        ${price.amount},
        ${price.currency},
        ${payment_method},
        'pending',
        NOW()
      )
      RETURNING id, amount_cents, currency
    ` as any[]

    if (orderResult.length === 0) {
      throw new Error('创建订单失败')
    }

    const order = orderResult[0]

    // 根据支付方式处理
    let paymentResponse: any = {
      order_id: order.id,
      amount: order.amount_cents,
      currency: order.currency,
      payment_method
    }

    if (payment_method === 'stripe') {
      // Stripe支付处理
      paymentResponse.payment_url = `https://checkout.stripe.com/pay/${order.id}`
      paymentResponse.message = '即将跳转到Stripe支付页面'
    } else if (payment_method === 'wechat' || payment_method === 'alipay') {
      // 微信/支付宝支付处理
      // 这里应该调用相应的支付SDK生成支付二维码或跳转链接
      paymentResponse.qr_code = `data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==`
      paymentResponse.message = '请使用手机扫描二维码完成支付'
    }

    return NextResponse.json({
      success: true,
      order: paymentResponse
    })

  } catch (error) {
    console.error('创建订单失败:', error)
    return NextResponse.json(
      { error: '创建订单失败，请稍后重试' },
      { status: 500 }
    )
  }
}

// 获取订单状态
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const orderId = searchParams.get('order_id')
    const userId = parseInt(session.user.id)

    if (!orderId) {
      return NextResponse.json({ error: '缺少订单ID' }, { status: 400 })
    }

    // 查询订单状态
    const orderResult = await prisma.$queryRaw`
      SELECT 
        o.id,
        o.status,
        o.amount_cents,
        o.currency,
        o.provider,
        o.created_at,
        us.status as subscription_status
      FROM orders o
      LEFT JOIN user_subscriptions us ON o.user_id = us.user_id
      WHERE o.id = ${parseInt(orderId)} AND o.user_id = ${userId}
      ORDER BY us.created_at DESC
      LIMIT 1
    ` as any[]

    if (orderResult.length === 0) {
      return NextResponse.json({ error: '订单不存在' }, { status: 404 })
    }

    const order = orderResult[0]

    return NextResponse.json({
      order_id: order.id,
      status: order.status,
      amount: order.amount_cents,
      currency: order.currency,
      provider: order.provider,
      created_at: order.created_at,
      subscription_status: order.subscription_status
    })

  } catch (error) {
    console.error('查询订单状态失败:', error)
    return NextResponse.json(
      { error: '查询订单状态失败' },
      { status: 500 }
    )
  }
}
