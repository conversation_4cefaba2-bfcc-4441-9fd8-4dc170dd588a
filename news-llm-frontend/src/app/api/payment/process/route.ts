import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function POST(request: NextRequest) {
  try {
    console.log('支付处理API被调用')

    const session = await getServerSession(authOptions)
    console.log('用户会话:', session?.user?.id)

    if (!session?.user?.id) {
      console.log('用户未登录')
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const userId = parseInt(session.user.id)
    const { planId, paymentMethod, market } = await request.json()

    console.log('支付参数:', { userId, planId, paymentMethod, market })

    // 定义套餐信息
    const planInfo: Record<string, any> = {
      'pro-monthly': {
        name: 'Pro 月付',
        period: 'monthly',
        price: { cn: 1900, us: 699 }, // 以分为单位
        description: 'News-LLM Pro 月付订阅'
      },
      'pro-yearly': {
        name: 'Pro 年付',
        period: 'yearly',
        price: { cn: 12800, us: 5900 }, // 以分为单位
        description: 'News-LLM Pro 年付订阅'
      }
    }

    const plan = planInfo[planId]
    if (!plan) {
      return NextResponse.json({ error: 'Invalid plan' }, { status: 400 })
    }

    const amount = plan.price[market as 'cn' | 'us']
    const currency = market === 'cn' ? 'CNY' : 'USD'

    // 生成订单号
    const orderId = `ORDER_${Date.now()}_${Math.random().toString(36).substr(2, 9).toUpperCase()}`

    // 获取支付方式显示名称
    const paymentMethodNames: Record<string, string> = {
      'wechat': '微信支付',
      'alipay': '支付宝',
      'stripe': '信用卡支付'
    }

    try {
      // 模拟支付处理时间
      await new Promise(resolve => setTimeout(resolve, 2000))

      // 首先确保plans表中有对应的套餐记录
      const planQuery = `
        INSERT INTO plans (market, name, period, price_cents, currency, is_active, created_at, updated_at)
        VALUES ($1, $2, $3, $4, $5, true, NOW(), NOW())
        ON CONFLICT (market, name, period) DO UPDATE SET
          price_cents = EXCLUDED.price_cents,
          currency = EXCLUDED.currency,
          updated_at = NOW()
        RETURNING id
      `

      const planResult = await prisma.$queryRawUnsafe(
        planQuery,
        market, // 'cn' 或 'us'
        plan.name, // 套餐名称，如 'Pro 月付' 或 'Pro 年付'
        plan.period, // 'monthly' 或 'yearly'
        amount,
        currency
      ) as any[]

      const planDbId = planResult[0]?.id

      // 创建订单记录（包含plan_id）
      const orderQuery = `
        INSERT INTO orders (
          user_id,
          plan_id,
          amount_cents,
          currency,
          provider,
          status,
          created_at
        ) VALUES ($1, $2, $3, $4, $5, $6, NOW())
        RETURNING id
      `

      const orderResult = await prisma.$queryRawUnsafe(
        orderQuery,
        userId,
        planDbId, // 关联套餐ID
        amount,
        currency,
        paymentMethod, // 使用原始的支付方式标识
        'paid' // 模拟支付成功
      ) as any[]

      const orderDbId = orderResult[0]?.id

      // 更新用户订阅状态
      const now = new Date()
      const periodEnd = new Date(now)
      
      if (plan.period === 'yearly') {
        periodEnd.setFullYear(periodEnd.getFullYear() + 1)
      } else {
        periodEnd.setMonth(periodEnd.getMonth() + 1)
      }

      // 检查用户是否已有订阅记录
      const existingSubQuery = `
        SELECT id FROM user_subscriptions WHERE user_id = $1 LIMIT 1
      `
      const existingResult = await prisma.$queryRawUnsafe(existingSubQuery, userId)
      const existingSubs = existingResult as any[]

      if (existingSubs.length > 0) {
        // 更新现有订阅
        const updateSubQuery = `
          UPDATE user_subscriptions
          SET
            plan_id = $1,
            status = 'active',
            plan_name = $2,
            current_period_start_at = $3,
            current_period_end_at = $4,
            updated_at = CURRENT_TIMESTAMP
          WHERE user_id = $5
        `
        await prisma.$queryRawUnsafe(
          updateSubQuery,
          planDbId, // 关联套餐ID
          plan.name,
          now,
          periodEnd,
          userId
        )
      } else {
        // 创建新订阅
        const createSubQuery = `
          INSERT INTO user_subscriptions (
            user_id, plan_id, status, plan_name,
            current_period_start_at, current_period_end_at,
            created_at, updated_at
          ) VALUES ($1, $2, 'active', $3, $4, $5, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        `
        await prisma.$queryRawUnsafe(
          createSubQuery,
          userId,
          planDbId, // 关联套餐ID
          plan.name,
          now,
          periodEnd
        )
      }

      console.log('支付处理完成，用户已升级为付费用户')

      return NextResponse.json({
        success: true,
        message: '支付成功',
        orderId,
        subscription: {
          status: 'active',
          plan_name: plan.name,
          period_end: periodEnd.toISOString()
        }
      })

    } catch (dbError) {
      console.error('数据库操作失败:', dbError)
      return NextResponse.json(
        { error: 'Database operation failed' },
        { status: 500 }
      )
    }

  } catch (error) {
    console.error('支付处理失败:', error)
    console.error('错误详情:', error instanceof Error ? error.message : String(error))
    return NextResponse.json(
      {
        error: 'Payment processing failed',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    )
  }
}
