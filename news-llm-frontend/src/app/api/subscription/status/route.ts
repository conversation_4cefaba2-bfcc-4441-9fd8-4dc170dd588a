import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    // 验证用户认证
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const userId = parseInt(session.user.id)

    // 查询用户订阅状态
    const subscriptionQuery = `
      SELECT
        status,
        plan_name,
        trial_start_at,
        trial_end_at,
        current_period_start_at,
        current_period_end_at,
        created_at
      FROM user_subscriptions
      WHERE user_id = $1
      ORDER BY created_at DESC
      LIMIT 1
    `

    const subscriptionResult = await prisma.$queryRawUnsafe(subscriptionQuery, userId)
    const subscriptions = subscriptionResult as any[]

    // 获取用户订阅源数量
    const subscriptionCountQuery = `
      SELECT COUNT(*) as count
      FROM user_feed_subscriptions
      WHERE user_id = $1 AND is_system_curated = false
    `

    const countResult = await prisma.$queryRawUnsafe(subscriptionCountQuery, userId)
    const counts = countResult as any[]
    const subscriptionSourcesCount = parseInt(counts[0]?.count || '0')

    // 默认状态（免费用户）
    let subscriptionData = {
      status: 'free' as const,
      plan_name: '免费版',
      trial_availed: false,
      subscription_sources_count: subscriptionSourcesCount,
      subscription_sources_limit: 10, // 免费用户限制
      current_period_end_at: null,
      trial_end_at: null
    }

    // 如果有订阅记录，更新状态
    if (subscriptions.length > 0) {
      const sub = subscriptions[0]

      // 检查试用是否过期
      if (sub.status === 'trialing' && sub.trial_end_at) {
        const now = new Date()
        const trialEnd = new Date(sub.trial_end_at)
        if (now > trialEnd) {
          // 试用已过期，更新状态为expired
          await prisma.$queryRawUnsafe(`
            UPDATE user_subscriptions
            SET status = 'expired', updated_at = CURRENT_TIMESTAMP
            WHERE user_id = $1 AND status = 'trialing'
          `, userId)
          subscriptionData.status = 'expired'
        } else {
          subscriptionData.status = 'trialing'
          subscriptionData.trial_end_at = sub.trial_end_at
        }
      } else {
        subscriptionData.status = sub.status
      }

      // 检查付费订阅是否过期
      if (sub.status === 'active' && sub.current_period_end_at) {
        const now = new Date()
        const periodEnd = new Date(sub.current_period_end_at)
        if (now > periodEnd) {
          // 订阅已过期
          await prisma.$queryRawUnsafe(`
            UPDATE user_subscriptions
            SET status = 'expired', updated_at = CURRENT_TIMESTAMP
            WHERE user_id = $1 AND status = 'active'
          `, userId)
          subscriptionData.status = 'expired'
        }
      }

      subscriptionData.plan_name = sub.plan_name || '专业版'
      subscriptionData.current_period_end_at = sub.current_period_end_at
      subscriptionData.trial_availed = sub.trial_start_at ? true : false

      // 付费用户或试用用户的订阅源限制
      if (subscriptionData.status === 'active' || subscriptionData.status === 'trialing') {
        subscriptionData.subscription_sources_limit = 100
      }
    }

    return NextResponse.json(subscriptionData)

  } catch (error) {
    console.error('获取订阅状态失败:', error)
    return NextResponse.json(
      { error: '获取订阅状态失败' },
      { status: 500 }
    )
  }
}
