import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    // 验证用户认证
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const userId = parseInt(searchParams.get('user_id') || session.user.id)

    // 验证用户ID
    if (userId !== parseInt(session.user.id)) {
      return NextResponse.json({ error: '无权访问' }, { status: 403 })
    }

    // 获取用户订阅状态
    const subscriptionQuery = `
      SELECT 
        us.status,
        us.trial_availed,
        us.trial_end_at,
        us.current_period_end_at,
        us.auto_renew,
        p.name as plan_name,
        p.period,
        p.price_cents,
        p.currency
      FROM user_subscriptions us
      LEFT JOIN plans p ON us.plan_id = p.id
      WHERE us.user_id = $1
      ORDER BY us.created_at DESC
      LIMIT 1
    `

    const subscriptionResult = await prisma.$queryRawUnsafe(subscriptionQuery, userId)
    const subscriptions = subscriptionResult as any[]

    // 获取用户订阅源数量
    const subscriptionCountQuery = `
      SELECT COUNT(*) as count
      FROM user_feed_subscriptions
      WHERE user_id = $1
    `

    const countResult = await prisma.$queryRawUnsafe(subscriptionCountQuery, userId)
    const counts = countResult as any[]
    const subscriptionSourcesCount = parseInt(counts[0]?.count || '0')

    // 默认状态（免费用户）
    let subscriptionData = {
      status: 'free' as const,
      trial_availed: false,
      subscription_sources_count: subscriptionSourcesCount,
      subscription_sources_limit: 10, // 免费用户限制
      plan_name: null,
      current_period_end_at: null,
      trial_end_at: null,
      auto_renew: false
    }

    // 如果有订阅记录，更新状态
    if (subscriptions.length > 0) {
      const sub = subscriptions[0]
      
      // 检查试用是否过期
      if (sub.status === 'trialing' && sub.trial_end_at) {
        const now = new Date()
        const trialEnd = new Date(sub.trial_end_at)
        if (now > trialEnd) {
          // 试用已过期，更新状态
          await prisma.$queryRaw`
            UPDATE user_subscriptions 
            SET status = 'expired', updated_at = NOW()
            WHERE user_id = ${userId} AND status = 'trialing'
          `
          subscriptionData.status = 'expired'
        } else {
          subscriptionData.status = 'trialing'
          subscriptionData.trial_end_at = sub.trial_end_at
        }
      } else {
        subscriptionData.status = sub.status
      }

      // 检查付费订阅是否过期
      if (sub.status === 'active' && sub.current_period_end_at) {
        const now = new Date()
        const periodEnd = new Date(sub.current_period_end_at)
        if (now > periodEnd) {
          // 订阅已过期
          await prisma.$queryRaw`
            UPDATE user_subscriptions 
            SET status = 'expired', updated_at = NOW()
            WHERE user_id = ${userId} AND status = 'active'
          `
          subscriptionData.status = 'expired'
        }
      }

      subscriptionData.trial_availed = sub.trial_availed || false
      subscriptionData.plan_name = sub.plan_name
      subscriptionData.current_period_end_at = sub.current_period_end_at
      subscriptionData.auto_renew = sub.auto_renew || false

      // 付费用户或试用用户的订阅源限制
      if (subscriptionData.status === 'active' || subscriptionData.status === 'trialing') {
        subscriptionData.subscription_sources_limit = 100
      }
    }

    return NextResponse.json(subscriptionData)

  } catch (error) {
    console.error('获取订阅状态失败:', error)
    return NextResponse.json(
      { error: '获取订阅状态失败' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    // 验证用户认证
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { action, user_id } = await request.json()
    const userId = parseInt(user_id)

    // 验证用户ID
    if (userId !== parseInt(session.user.id)) {
      return NextResponse.json({ error: '无权操作' }, { status: 403 })
    }

    if (action === 'cancel') {
      // 取消订阅
      const result = await prisma.$queryRaw`
        UPDATE user_subscriptions 
        SET status = 'canceled', auto_renew = false, updated_at = NOW()
        WHERE user_id = ${userId} AND status = 'active'
        RETURNING id
      ` as any[]

      if (result.length === 0) {
        return NextResponse.json(
          { error: '没有找到可取消的订阅' },
          { status: 400 }
        )
      }

      return NextResponse.json({
        success: true,
        message: '订阅已取消，服务将持续到当前付费周期结束'
      })
    }

    return NextResponse.json(
      { error: '不支持的操作' },
      { status: 400 }
    )

  } catch (error) {
    console.error('订阅操作失败:', error)
    return NextResponse.json(
      { error: '操作失败，请稍后重试' },
      { status: 500 }
    )
  }
}
