import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function PUT(request: NextRequest) {
  try {
    // 验证用户认证
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const userId = parseInt(session.user.id)
    const body = await request.json()
    const { language } = body

    if (!language || !['zh', 'en'].includes(language)) {
      return NextResponse.json({
        error: '语言参数无效，只支持 zh 或 en'
      }, { status: 400 })
    }

    // 转换为数据库格式
    const dbLanguage = language === 'zh' ? 'zh_CN' : 'en_US'

    // 更新用户语言设置
    const updatedUser = await prisma.users.update({
      where: { id: userId },
      data: {
        language: dbLanguage,
        updated_at: new Date()
      },
      select: {
        id: true,
        language: true,
        updated_at: true
      }
    })

    return NextResponse.json({
      success: true,
      message: '语言设置更新成功',
      data: {
        id: updatedUser.id,
        language: language, // 返回前端格式
        updatedAt: updatedUser.updated_at
      }
    })
  } catch (error) {
    console.error('更新语言设置失败:', error)
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 })
  }
}
