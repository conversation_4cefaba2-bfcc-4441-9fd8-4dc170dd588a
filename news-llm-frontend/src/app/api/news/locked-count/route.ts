import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    // 验证用户认证
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const userId = parseInt(searchParams.get('user_id') || session.user.id)

    // 验证用户ID
    if (userId !== parseInt(session.user.id)) {
      return NextResponse.json({ error: '无权访问' }, { status: 403 })
    }

    // 检查用户订阅状态
    const subscriptionQuery = `
      SELECT status, trial_end_at, current_period_end_at
      FROM user_subscriptions
      WHERE user_id = $1
      ORDER BY created_at DESC
      LIMIT 1
    `

    const subscriptionResult = await prisma.$queryRawUnsafe(subscriptionQuery, userId)
    const subscriptions = subscriptionResult as any[]

    // 判断用户是否为付费/试用用户
    let isPro = false
    if (subscriptions.length > 0) {
      const sub = subscriptions[0]
      const now = new Date()

      if (sub.status === 'active') {
        // 检查付费订阅是否过期
        if (!sub.current_period_end_at || new Date(sub.current_period_end_at) > now) {
          isPro = true
        }
      } else if (sub.status === 'trialing') {
        // 检查试用是否过期
        if (!sub.trial_end_at || new Date(sub.trial_end_at) > now) {
          isPro = true
        }
      }
    }

    // 如果是付费/试用用户，没有锁定内容
    if (isPro) {
      return NextResponse.json({
        count: 0,
        is_pro: true
      })
    }

    // 获取今日高分锁定内容数量
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    const tomorrow = new Date(today)
    tomorrow.setDate(tomorrow.getDate() + 1)

    // 查询今日发布的≥4.0★内容数量（D+1模式）
    const lockedCountQuery = `
      SELECT COUNT(*) as count
      FROM entries e
      LEFT JOIN feeds f ON e.feed_id = f.id
      LEFT JOIN user_feed_subscriptions ufs ON (f.id = ufs.feed_id AND ufs.user_id = $1)
      WHERE (
        -- 用户个人订阅的源
        ufs.user_id = $1
        OR
        -- 系统推荐源（is_system_curated = true）
        ufs.is_system_curated = true
      )
      AND e.ai_score >= 4.0
      AND e.published_at >= $2
      AND e.published_at < $3
    `

    const countResult = await prisma.$queryRawUnsafe(
      lockedCountQuery,
      userId,
      today,
      tomorrow
    )
    const counts = countResult as any[]
    const lockedCount = parseInt(counts[0]?.count || '0')

    return NextResponse.json({
      count: lockedCount,
      is_pro: false,
      unlock_mode: 'D+1',
      unlock_date: tomorrow.toISOString()
    })

  } catch (error) {
    console.error('获取锁定内容数量失败:', error)
    return NextResponse.json(
      { error: '获取锁定内容数量失败' },
      { status: 500 }
    )
  }
}
