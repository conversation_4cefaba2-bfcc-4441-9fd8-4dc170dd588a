import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    // 获取用户认证信息（可选）
    const session = await getServerSession(authOptions)
    const userId = session ? parseInt(session.user.id) : null
    const { searchParams } = new URL(request.url)

    // 获取查询参数
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const category = searchParams.get('category') || '全部'
    const sourceType = searchParams.get('source_type') || '全部' // 全部 | 精选推荐 | 个人订阅
    const feedSource = searchParams.get('feed_source') || '全部' // 具体的feed源
    const minRating = parseFloat(searchParams.get('min_rating') || '3.5')
    const onlyRated = searchParams.get('only_rated') === 'true' // 仅看已评分
    const sort = searchParams.get('sort') || 'newest'

    // 获取admin用户订阅的feeds作为系统推荐
    const adminUser = await prisma.users.findFirst({
      where: { is_admin: true },
      select: { id: true }
    })

    let systemRecommendedFeedIds: number[] = []
    if (adminUser) {
      const adminSubscriptions = await prisma.user_feed_subscriptions.findMany({
        where: { user_id: adminUser.id },
        select: { feed_id: true }
      })
      systemRecommendedFeedIds = adminSubscriptions.map(sub => sub.feed_id)
    }

    // 获取用户订阅的feed IDs（如果已登录）
    let userSubscriptions: any[] = []
    let subscribedFeedIds: number[] = []
    let personalFeedIds: number[] = []
    let systemFeedIds: number[] = []

    if (userId) {
      userSubscriptions = await prisma.user_feed_subscriptions.findMany({
        where: { user_id: userId },
        select: { feed_id: true, is_system_curated: true }
      })

      subscribedFeedIds = userSubscriptions.map(sub => sub.feed_id)
      personalFeedIds = userSubscriptions
        .filter(sub => !sub.is_system_curated)
        .map(sub => sub.feed_id)
      systemFeedIds = userSubscriptions
        .filter(sub => sub.is_system_curated)
        .map(sub => sub.feed_id)
    } else {
      // 未登录用户使用admin的订阅作为默认内容
      subscribedFeedIds = systemRecommendedFeedIds
      systemFeedIds = systemRecommendedFeedIds
      personalFeedIds = []
    }

    // 构建查询条件
    const where: any = {
      ai_processed: true,
      ai_score: {
        gte: minRating
      }
    }

    // 分类筛选
    if (category !== '全部') {
      where.OR = [
        { category: category },
        { tags: { has: category } }
      ]
    }

    // 排序条件 - 统一按时间倒序排序
    const orderBy: any = { published_at: 'desc' }

    // 构建Prisma查询条件
    const whereCondition: any = {}

    // 根据onlyRated参数决定是否要求AI处理
    if (onlyRated) {
      whereCondition.ai_processed = true
      whereCondition.ai_score = {
        gte: minRating
      }
    }

    // 基于用户订阅和系统推荐过滤feeds
    if (sourceType === '全部') {
      // 显示所有可用的feeds（用户订阅 + 系统推荐）
      const allAvailableFeedIds = [...new Set([...subscribedFeedIds, ...systemRecommendedFeedIds])]
      if (allAvailableFeedIds.length > 0) {
        whereCondition.feed_id = { in: allAvailableFeedIds }
      } else {
        whereCondition.id = -1 // 没有任何可用的feed
      }
    } else if (sourceType === '精选推荐') {
      // 只显示系统推荐的feeds（admin用户订阅的源）
      if (systemRecommendedFeedIds.length > 0) {
        whereCondition.feed_id = { in: systemRecommendedFeedIds }
      } else {
        whereCondition.id = -1 // 没有系统推荐
      }
    } else if (sourceType === '个人订阅') {
      // 显示个人订阅的feeds（仅限已登录用户）
      if (!userId) {
        // 未登录用户无法访问个人订阅
        whereCondition.id = -1
      } else if (personalFeedIds.length > 0) {
        let targetFeedIds = personalFeedIds

        // 如果指定了具体的feed源，进一步筛选
        if (feedSource !== '全部') {
          // 尝试将feedSource解析为数字ID
          const feedId = parseInt(feedSource)
          const feedIdBigInt = BigInt(feedId)
          if (!isNaN(feedId) && personalFeedIds.includes(feedIdBigInt)) {
            targetFeedIds = [feedIdBigInt]
          } else {
            // 如果不是数字ID，则按标题模糊匹配（兼容旧逻辑）
            const specificFeeds = await prisma.feeds.findMany({
              where: {
                id: { in: personalFeedIds },
                title: { contains: feedSource }
              },
              select: { id: true }
            })
            targetFeedIds = specificFeeds.map(f => f.id)
          }
        }

        if (targetFeedIds.length > 0) {
          whereCondition.feed_id = { in: targetFeedIds }
        } else {
          whereCondition.id = -1 // 没有匹配的feed
        }
      } else {
        whereCondition.id = -1 // 没有个人订阅
      }
    }

    if (category && category !== '全部') {
      whereCondition.OR = [
        { category: category },
        { tags: { has: category } }
      ]
    }

    const search = searchParams.get('search');
    if (search) {
      whereCondition.OR = [
        { title: { contains: search, mode: 'insensitive' } },
        { content: { contains: search, mode: 'insensitive' } }
      ]
    }

    // 调试日志
    console.log('API查询条件:', whereCondition)
    console.log('查询参数:', { page, limit, onlyRated, sourceType, category })

    // 使用Prisma查询
    const [entries, total] = await Promise.all([
      prisma.entries.findMany({
        where: whereCondition,
        include: {
          feed: {
            select: {
              title: true,
              site_url: true
            }
          }
        },
        orderBy: orderBy,
        skip: (page - 1) * limit,
        take: limit
      }),
      prisma.entries.count({
        where: whereCondition
      })
    ]);

    console.log('查询结果:', { entriesCount: entries.length, total })

    // 转换数据格式
    const formattedNews = entries.map(entry => ({
      id: entry.id.toString(),
      title: entry.title,
      summary: entry.ai_summary || '',
      content: entry.content || '',
      source: entry.feed?.title || '',
      author: entry.author || '',
      url: entry.url,
      publishedAt: new Date(entry.published_at).toISOString(),
      tags: entry.tags,
      aiScore: entry.ai_score ? parseFloat(entry.ai_score.toString()) : 0,
      sentiment: entry.sentiment || 'neutral',
      readTime: entry.reading_time,
      isBookmarked: entry.starred,
      isRead: entry.status === 'read',
      isSystem: true,
      minifluxEntryId: parseInt(entry.id.toString()),
      category: entry.category || '新闻',
      qualityScore: entry.quality_score ? parseFloat(entry.quality_score.toString()) : 0
    }))

    const totalPages = Math.ceil(total / limit)

    // 返回结果
    return NextResponse.json({
      news: formattedNews,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasMore: page < totalPages
      },
      filters: {
        category,
        sourceType,
        minRating,
        sort
      }
    })

  } catch (error) {
    console.error('获取新闻列表失败:', error)
    return NextResponse.json(
      { error: '获取新闻列表失败' },
      { status: 500 }
    )
  }
}
