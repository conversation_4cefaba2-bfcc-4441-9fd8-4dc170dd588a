import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import bcrypt from 'bcryptjs'

export async function POST(request: NextRequest) {
  try {
    // 验证用户认证
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const userId = parseInt(session.user.id)
    const body = await request.json()
    const { currentPassword, newPassword } = body

    if (!currentPassword || !newPassword) {
      return NextResponse.json({ 
        error: '缺少必要参数' 
      }, { status: 400 })
    }

    if (newPassword.length < 6) {
      return NextResponse.json({ 
        error: '新密码长度至少6位' 
      }, { status: 400 })
    }

    // 获取用户当前密码哈希
    const user = await prisma.users.findUnique({
      where: { id: userId },
      select: {
        id: true,
        password: true
      }
    })

    if (!user) {
      return NextResponse.json({ error: '用户不存在' }, { status: 404 })
    }

    if (!user.password) {
      return NextResponse.json({ 
        error: '用户未设置密码，请联系管理员' 
      }, { status: 400 })
    }

    // 验证当前密码
    const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.password)
    
    if (!isCurrentPasswordValid) {
      return NextResponse.json({ 
        error: '当前密码不正确' 
      }, { status: 401 })
    }

    // 生成新密码哈希
    const saltRounds = 12
    const newPasswordHash = await bcrypt.hash(newPassword, saltRounds)

    // 更新密码
    const updatedUser = await prisma.users.update({
      where: { id: userId },
      data: {
        password: newPasswordHash,
        updated_at: new Date()
      },
      select: {
        id: true,
        updated_at: true
      }
    })

    return NextResponse.json({
      success: true,
      message: '密码修改成功',
      data: {
        updatedAt: updatedUser.updated_at
      }
    })
  } catch (error) {
    console.error('修改密码失败:', error)
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 })
  }
}
