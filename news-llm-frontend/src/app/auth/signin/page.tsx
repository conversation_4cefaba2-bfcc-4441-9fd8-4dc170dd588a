'use client'

import { useState } from 'react'
import { signIn, getSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'

export default function SignIn() {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const router = useRouter()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    const result = await signIn('credentials', {
      email,
      password,
      redirect: false,
    })

    if (result?.ok) {
      // 检查是否有保存的重定向目标
      const redirectTo = sessionStorage.getItem('redirectAfterLogin')
      if (redirectTo) {
        sessionStorage.removeItem('redirectAfterLogin')
        router.push(redirectTo)
      } else {
        router.push('/news') // 默认跳转到新闻阅读页面
      }
    } else {
      setError('登录失败，请检查邮箱和密码')
    }
    setLoading(false)
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full">
        <div className="bg-white border-2 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,1)]">
          {/* 票券头部 */}
          <div className="bg-black text-white p-4 text-center">
            <h2 className="text-xl font-bold tracking-wider font-mono">SIGN IN</h2>
            <p className="text-xs opacity-80 mt-1">登录 / LOGIN</p>
          </div>

          <div className="p-6">
            <form className="space-y-4" onSubmit={handleSubmit}>
              {error && (
                <div className="bg-red-100 border border-red-400 text-red-700 px-3 py-2 text-sm font-mono">
                  {error}
                </div>
              )}

              <div className="space-y-4">
                <div>
                  <label htmlFor="email" className="block text-sm font-bold text-black mb-2 font-mono">
                    EMAIL
                  </label>
                  <input
                    id="email"
                    name="email"
                    type="email"
                    autoComplete="email"
                    required
                    className="w-full px-3 py-2 border border-gray-300 focus:border-black bg-white transition-all font-mono text-sm"
                    placeholder="Enter your email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                  />
                </div>
                <div>
                  <label htmlFor="password" className="block text-sm font-bold text-black mb-2 font-mono">
                    PASSWORD
                  </label>
                  <input
                    id="password"
                    name="password"
                    type="password"
                    autoComplete="current-password"
                    required
                    className="w-full px-3 py-2 border border-gray-300 focus:border-black bg-white transition-all font-mono text-sm"
                    placeholder="Enter your password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                  />
                </div>
              </div>

              <div className="pt-4">
                <button
                  type="submit"
                  disabled={loading}
                  className="w-full py-3 bg-black text-white font-mono text-sm font-bold hover:bg-gray-800 transition-all border border-black shadow-[2px_2px_0px_0px_rgba(0,0,0,0.3)] disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {loading ? 'SIGNING IN...' : 'SIGN IN'}
                </button>
              </div>

              <div className="text-center pt-4 border-t border-gray-200 mt-4">
                <span className="text-sm text-gray-600 font-mono">
                  NO ACCOUNT?{' '}
                  <Link href="/auth/signup" className="font-bold text-black hover:underline">
                    SIGN UP
                  </Link>
                </span>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  )
}
