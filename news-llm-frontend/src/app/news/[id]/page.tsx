'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { useParams } from 'next/navigation'
import AuthLayout from '@/components/AuthLayout'

interface NewsDetail {
  id: string
  title: string
  content: string
  summary: string
  source: string
  author?: string
  url: string
  publishedAt: string
  tags: string[]
  readTime: number
  sentiment: 'positive' | 'neutral' | 'negative'
  aiScore: number
  qualityScore: number
  category: string
  isRead: boolean
  relatedNews: RelatedNews[]
}

interface RelatedNews {
  id: string
  title: string
  source: string
  publishedAt: string
}

export default function NewsDetailPage() {
  const params = useParams()
  const [news, setNews] = useState<NewsDetail | null>(null)
  const [loading, setLoading] = useState(true)

  // API调用函数
  const fetchNewsDetail = async (id: string) => {
    try {
      setLoading(true)
      const response = await fetch(`/api/news/${id}`)
      if (!response.ok) {
        throw new Error('Failed to fetch news detail')
      }

      const data = await response.json()
      setNews(data)

    } catch (error) {
      console.error('获取新闻详情失败:', error)
      // 如果API失败，使用备用模拟数据
      const mockNews: NewsDetail = {
      id: params.id as string,
      title: 'OpenAI发布GPT-4 Turbo，性能提升显著',
      content: `
        <p>OpenAI今天宣布推出GPT-4 Turbo模型，这是其最新的大型语言模型，在保持高质量输出的同时，显著提升了处理速度和成本效益。</p>
        
        <h2>主要改进</h2>
        <p>GPT-4 Turbo相比前代模型有以下几个重要改进：</p>
        <ul>
          <li><strong>更快的响应速度</strong>：处理速度提升了约40%</li>
          <li><strong>更低的成本</strong>：API调用成本降低了50%</li>
          <li><strong>更大的上下文窗口</strong>：支持128K tokens的上下文长度</li>
          <li><strong>更准确的输出</strong>：在各项基准测试中表现更优</li>
        </ul>
        
        <h2>技术细节</h2>
        <p>GPT-4 Turbo采用了全新的架构优化，包括：</p>
        <p>1. <strong>模型压缩技术</strong>：通过先进的压缩算法，在保持性能的同时减少了模型大小</p>
        <p>2. <strong>推理优化</strong>：优化了推理过程，显著提升了生成速度</p>
        <p>3. <strong>内存管理</strong>：改进了内存使用效率，支持更长的对话历史</p>
        
        <h2>应用场景</h2>
        <p>GPT-4 Turbo特别适合以下应用场景：</p>
        <p>• 长文档分析和总结<br>
        • 代码生成和调试<br>
        • 多轮对话系统<br>
        • 内容创作和编辑<br>
        • 数据分析和报告生成</p>
        
        <h2>市场影响</h2>
        <p>业界专家认为，GPT-4 Turbo的发布将进一步推动AI应用的普及，特别是在企业级应用方面。更低的成本和更高的性能使得更多企业能够负担得起高质量的AI服务。</p>
        
        <p>OpenAI CEO Sam Altman表示："GPT-4 Turbo代表了我们在AI技术方面的又一重大突破。我们致力于让AI技术更加普及和实用。"</p>
      `,
      summary: 'OpenAI宣布推出GPT-4 Turbo模型，在保持高质量输出的同时，显著提升了处理速度和成本效益...',
      source: '36氪',
      author: '张三',
      url: 'https://example.com/news/1',
      publishedAt: '2024-01-15T10:30:00Z',
      tags: ['AI', '科技', 'OpenAI', 'GPT-4'],
      readTime: 5,
      sentiment: 'positive',
      qualityScore: 8.5,
      relatedNews: [
        {
          id: '2',
          title: '2024年科技趋势预测：AI将如何改变我们的生活',
          source: 'TechCrunch',
          publishedAt: '2024-01-15T09:15:00Z'
        },
        {
          id: '3',
          title: '创业公司如何在AI时代找到机会',
          source: '知乎日报',
          publishedAt: '2024-01-15T08:45:00Z'
        }
      ]
    }
      // 暂时使用模拟数据，等API修复后移除
      setNews(mockNews)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (params.id) {
      fetchNewsDetail(params.id as string)
    }
  }, [params.id])

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getSentimentColor = (sentiment: string) => {
    switch (sentiment) {
      case 'positive': return 'text-green-600 bg-green-100'
      case 'negative': return 'text-red-600 bg-red-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const getSentimentText = (sentiment: string) => {
    switch (sentiment) {
      case 'positive': return '积极'
      case 'negative': return '消极'
      default: return '中性'
    }
  }

  if (loading) {
    return (
      <AuthLayout>
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="bg-white border-2 border-black p-8 shadow-[4px_4px_0px_0px_rgba(0,0,0,1)]">
            <div className="flex items-center space-x-4">
              <div className="w-6 h-6 border-2 border-black border-t-transparent animate-spin"></div>
              <span className="font-mono font-bold text-black">LOADING ARTICLE...</span>
            </div>
          </div>
        </div>
      </AuthLayout>
    )
  }

  if (!news) {
    return (
      <AuthLayout>
        <div className="max-w-4xl mx-auto text-center py-12">
          <div className="text-gray-400 text-6xl mb-4">📰</div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">文章未找到</h2>
          <p className="text-gray-600 mb-6">抱歉，您要查看的文章不存在或已被删除。</p>
          <Link
            href="/news"
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg transition-colors"
          >
            返回新闻列表
          </Link>
        </div>
      </AuthLayout>
    )
  }

  return (
    <AuthLayout>
      <div className="max-w-4xl mx-auto px-4 py-6">
        {/* 返回按钮 */}
        <div className="mb-6">
          <Link
            href="/news"
            className="inline-flex items-center text-black hover:text-gray-600 transition-colors font-mono font-bold border border-gray-300 hover:border-black px-3 py-2"
          >
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth={2}>
              <path strokeLinecap="round" strokeLinejoin="round" d="M15 19l-7-7 7-7" />
            </svg>
            ← BACK TO NEWS
          </Link>
        </div>

        {/* 文章头部 */}
        <article className="bg-white border border-black shadow-[3px_3px_0px_0px_rgba(0,0,0,1)]">
          {/* 票券头部 */}
          <div className="bg-black text-white p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="bg-white text-black px-2 py-1 font-mono text-xs font-bold">
                  ARTICLE
                </div>
                <div>
                  <div className="text-xs font-mono opacity-80">SOURCE</div>
                  <div className="font-bold tracking-wide">{news.source}</div>
                </div>
              </div>
              <div className="text-right">
                <div className="text-xs font-mono opacity-80">QUALITY</div>
                <div className="text-lg font-bold">{news.qualityScore}/10</div>
              </div>
            </div>
          </div>

          <div className="p-6">
            <header className="mb-6">
              <h1 className="text-2xl font-bold text-black mb-4 leading-tight">
                {news.title}
              </h1>
              <div className="w-16 h-0.5 bg-black mb-4"></div>

              <div className="flex flex-wrap items-center gap-3 text-sm text-gray-600 mb-4 font-mono">
                {news.author && <span>AUTHOR: {news.author}</span>}
                <span>PUBLISHED: {formatDate(news.publishedAt)}</span>
                <span>READ TIME: {news.readTime} MIN</span>
              </div>

              <div className="flex flex-wrap items-center gap-2 mb-4">
                {news.tags.map((tag, index) => (
                  <span
                    key={tag}
                    className="px-2 py-1 bg-gray-100 text-black text-xs font-mono font-bold border border-gray-300"
                  >
                    #{index + 1} {tag}
                  </span>
                ))}
              </div>

              <div className="flex items-center gap-3 text-sm">
                <div className={`px-2 py-1 border border-gray-300 font-mono font-bold ${getSentimentColor(news.sentiment)}`}>
                  SENTIMENT: {getSentimentText(news.sentiment).toUpperCase()}
                </div>
              </div>
            </header>

            {/* 文章摘要 */}
            <div className="bg-gray-100 border border-gray-300 p-4 mb-6">
              <h3 className="font-bold text-black mb-2 font-mono">SUMMARY</h3>
              <p className="text-gray-700 leading-relaxed">{news.summary}</p>
            </div>

            {/* 文章内容 */}
            <div
              className="prose prose-lg max-w-none mb-6 article-content"
              dangerouslySetInnerHTML={{ __html: news.content }}
            />

            {/* 原文链接 */}
            <div className="border-t border-dashed border-gray-300 pt-4">
              <a
                href={news.url}
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center text-black hover:text-gray-600 font-mono font-bold border border-gray-300 hover:border-black px-3 py-2 transition-all"
              >
                VIEW ORIGINAL →
                <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth={2}>
                  <path strokeLinecap="round" strokeLinejoin="round" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                </svg>
              </a>
            </div>
          </div>
        </article>

        {/* 相关文章 */}
        {news.relatedNews.length > 0 && (
          <div className="mt-8">
            <div className="bg-white border border-black shadow-[2px_2px_0px_0px_rgba(0,0,0,1)]">
              <div className="bg-black text-white p-3 flex items-center justify-between">
                <div>
                  <h2 className="text-lg font-bold tracking-wider font-mono">RELATED</h2>
                  <p className="text-xs opacity-80">相关文章 / ARTICLES</p>
                </div>
                <div className="text-xl font-bold">→</div>
              </div>
              <div className="p-4 space-y-3">
                {news.relatedNews.map((related, index) => (
                  <Link
                    key={related.id}
                    href={`/news/${related.id}`}
                    className="block border border-gray-300 hover:border-black p-3 hover:shadow-[1px_1px_0px_0px_rgba(0,0,0,0.1)] transition-all duration-300"
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="text-xs font-mono text-gray-500 mb-1">
                          #{String(index + 1).padStart(2, '0')}
                        </div>
                        <h3 className="font-bold text-black mb-2 line-clamp-2 text-sm leading-tight">
                          {related.title}
                        </h3>
                        <div className="flex items-center text-xs text-gray-600 font-mono">
                          <span className="font-bold">{related.source}</span>
                          <span className="mx-2">|</span>
                          <span>{formatDate(related.publishedAt)}</span>
                        </div>
                      </div>
                      <div className="text-gray-400 ml-3">
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth={2}>
                          <path strokeLinecap="round" strokeLinejoin="round" d="M9 5l7 7-7 7" />
                        </svg>
                      </div>
                    </div>
                  </Link>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>
    </AuthLayout>
  )
}
