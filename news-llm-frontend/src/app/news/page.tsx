'use client'

import { useState, useEffect, useCallback, useMemo } from 'react'
import Link from 'next/link'
import { useSession } from 'next-auth/react'
import Navigation from '@/components/Navigation'
import LockedNewsCard from '@/components/LockedNewsCard'
import PremiumNewsCard from '@/components/PremiumNewsCard'
import SubscriptionStatus from '@/components/SubscriptionStatus'

interface NewsItem {
  id: string
  title: string
  summary: string
  content: string
  source: string
  url: string
  publishedAt: string
  tags: string[]
  readTime: number
  rating: number
  isRead: boolean
}

export default function NewsPage() {
  const { data: session } = useSession()
  const [news, setNews] = useState<NewsItem[]>([])
  const [loading, setLoading] = useState(false)
  const [loadingMore, setLoadingMore] = useState(false)
  const [hasMore, setHasMore] = useState(true)
  const [page, setPage] = useState(1)
  const [selectedCategory, setSelectedCategory] = useState('全部')
  const [selectedSourceType, setSelectedSourceType] = useState('全部') // 全部 | 精选推荐 | 个人订阅
  const [selectedFeed, setSelectedFeed] = useState('全部') // 在个人订阅下选择具体源
  const [minRating, setMinRating] = useState(3.8)
  const [onlyRated, setOnlyRated] = useState(false) // 仅看已评分
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0,
    hasMore: false
  })
  const [userSubscription, setUserSubscription] = useState<{
    status: 'free' | 'trialing' | 'active' | 'canceled' | 'expired'
    is_pro: boolean
  }>({ status: 'free', is_pro: false })
  const [personalFeeds, setPersonalFeeds] = useState<any[]>([])
  const [feedsLoading, setFeedsLoading] = useState(false)

  const categories = [
    '全部', '科技', 'AI', '财经',
    '创业', '互联网', '汽车', '医疗',
    '教育', '生活', '游戏', '区块链',
    '体育', '娱乐', '房产', '军事'
  ]
  // 根据登录状态动态生成源类型选项
  const sourceTypes = useMemo(() => {
    const baseTypes = ['全部', '精选推荐']
    if (session) {
      baseTypes.push('个人订阅')
    }
    return baseTypes
  }, [session])

  // 获取个人订阅源列表
  const fetchPersonalFeeds = useCallback(async () => {
    if (selectedSourceType !== '个人订阅' || !session) return

    try {
      setFeedsLoading(true)
      const response = await fetch('/api/subscriptions')
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to fetch subscriptions')
      }

      const data = await response.json()
      const personalSubs = data.personal || []

      // 转换为feed选项格式
      const feedOptions = [
        { id: '全部', title: '全部' },
        ...personalSubs.map((sub: any) => ({
          id: sub.feeds.id.toString(),
          title: sub.feeds.title
        }))
      ]

      setPersonalFeeds(feedOptions)
    } catch (error) {
      console.error('获取个人订阅失败:', error)
      // 设置默认选项，避免界面崩溃
      setPersonalFeeds([{ id: '全部', title: '全部' }])
    } finally {
      setFeedsLoading(false)
    }
  }, [selectedSourceType, session])

  // 获取用户订阅状态
  const fetchUserSubscription = useCallback(async () => {
    if (!session) {
      setUserSubscription({ status: 'free', is_pro: false })
      return
    }

    try {
      const response = await fetch('/api/subscription/status')
      if (response.ok) {
        const data = await response.json()
        setUserSubscription({
          status: data.status || 'free',
          is_pro: data.status === 'active' || data.status === 'trialing'
        })
      }
    } catch (error) {
      console.error('获取用户订阅状态失败:', error)
      // 默认为免费用户
      setUserSubscription({ status: 'free', is_pro: false })
    }
  }, [session])

  // 判断新闻是否应该锁定
  const isNewsLocked = useCallback((item: NewsItem) => {
    // 如果用户未登录，不锁定（但会在需要时提示登录）
    if (!session) return false
    
    // 如果是付费/试用用户，不锁定
    if (userSubscription.is_pro) return false

    // 如果AI评分低于4.0，不锁定
    const score = item.aiScore || item.rating || 0
    if (score < 4.0) return false

    // D+1模式：当天发布的高分内容锁定
    const today = new Date().toDateString()
    const publishDate = new Date(item.publishedAt).toDateString()
    return today === publishDate
  }, [session, userSubscription.is_pro])

  // API调用函数 - 使用useCallback避免不必要的重新创建，不依赖筛选条件
  const fetchNews = useCallback(async (pageNum: number = 1, append: boolean = false) => {
    try {
      const params = new URLSearchParams({
        page: pageNum.toString(),
        limit: '10',
        sort: 'newest'
      })

      const response = await fetch(`/api/news?${params}`)
      if (!response.ok) {
        throw new Error('Failed to fetch news')
      }

      const data = await response.json()

      if (append) {
        setNews(prev => {
          // 去重：只添加不存在的新闻
          const existingIds = new Set(prev.map(item => item.id))
          const newItems = data.news.filter(item => !existingIds.has(item.id))
          return [...prev, ...newItems]
        })
      } else {
        setNews(data.news)
      }

      setPagination(data.pagination)
      setHasMore(data.pagination.hasMore)
      setPage(data.pagination.page)

    } catch (error) {
      console.error('获取新闻失败:', error)
    }
  }, []) // 移除筛选条件依赖，改为纯客户端筛选

  // 生成模拟数据的函数
  const generateMockNews = (startId: number, count: number): NewsItem[] => {
    const sources = ['36氪', 'TechCrunch', '知乎日报', '虎嗅网', 'InfoQ', '少数派', 'IT之家', '钛媒体', '雷锋网', '极客公园']
    const mockCategories = ['科技', 'AI', '财经', '创业', '互联网', '汽车', '医疗', '教育', '生活', '游戏', '区块链', '体育', '娱乐', '房产', '军事']
    const titles = [
      'OpenAI发布GPT-5，多模态能力大幅提升',
      '比特币突破10万美元大关，创历史新高',
      '创业公司如何在AI时代找到机会',
      'Meta推出全新VR设备，虚拟现实体验再升级',
      '苹果发布iOS 18，AI功能全面集成',
      '特斯拉自动驾驶技术获得重大突破',
      '量子计算机商业化应用前景广阔',
      '5G网络建设加速，物联网迎来新机遇',
      '区块链技术在供应链管理中的应用',
      '人工智能助力医疗诊断准确率提升',
      '新能源汽车市场竞争日趋激烈',
      '云计算服务商争夺企业级市场',
      '机器人技术在制造业中的广泛应用',
      '数字货币监管政策逐步完善',
      '智能家居设备普及率持续上升',
      '网络安全威胁形势日益严峻',
      '在线教育平台用户增长迅速',
      '电商直播带货模式创新发展',
      '共享经济模式面临新的挑战',
      '绿色科技推动可持续发展'
    ]
    const summaries = [
      'GPT-5在图像理解、代码生成和推理能力方面都实现了大幅度，支持更长的上下文窗口，能够处理更复杂的多模态任务。新模型在香港进行中等教育体验，有望推动AI应用的新一轮发展。',
      '受机构投资者大量买入推动，比特币价格首次突破10万美元，分析师认为这标志着加密货币市场进入新的发展阶段，但也提醒投资者注意风险。',
      '在AI技术快速发展的今天，创业公司面临着前所未有的机遇和挑战。本文分析了几个关键策略，帮助创业者在激烈的市场竞争中找到自己的定位。',
      '最新发布的VR设备采用了更先进的显示技术和交互方式，为用户带来更加沉浸式的虚拟现实体验，预计将推动VR行业的快速发展。',
      '苹果在最新的iOS系统中深度集成了AI功能，包括智能助手、图像识别、语音处理等多个方面，为用户提供更加智能化的移动体验。',
      '特斯拉在自动驾驶技术方面取得重大进展，新的算法能够更好地处理复杂的交通场景，为完全自动驾驶的实现奠定了坚实基础。',
      '量子计算技术正在从实验室走向商业应用，在密码学、药物发现、金融建模等领域展现出巨大潜力，有望带来计算能力的革命性提升。',
      '随着5G网络建设的加速推进，物联网设备的连接速度和稳定性得到显著提升，为智慧城市、工业4.0等应用场景提供了强有力的技术支撑。',
      '区块链技术在供应链管理中的应用越来越广泛，通过提供透明、可追溯的记录系统，帮助企业提高供应链的效率和可信度。',
      '人工智能技术在医疗诊断领域的应用取得显著成果，通过深度学习算法分析医学影像，能够大幅提高疾病诊断的准确率和效率。',
      '新能源汽车市场竞争日趋激烈，各大厂商纷纷推出新产品，在续航里程、充电速度、智能化程度等方面不断创新，推动行业快速发展。',
      '云计算服务商正在加大对企业级市场的投入，通过提供更加专业化的解决方案和服务，满足不同行业客户的数字化转型需求。',
      '机器人技术在制造业中的应用越来越广泛，从简单的重复性工作到复杂的装配任务，机器人正在改变传统的生产方式。',
      '各国政府正在逐步完善数字货币的监管政策框架，在促进创新发展的同时，加强对风险的防控和管理。',
      '智能家居设备的普及率持续上升，消费者对于智能化生活的需求不断增长，推动了相关产业链的快速发展。',
      '网络安全威胁形势日益严峻，企业和个人都需要加强安全防护意识，采用更加先进的安全技术和管理措施。',
      '在线教育平台用户数量快速增长，疫情加速了教育数字化转型，在线学习已成为重要的教育方式。',
      '电商直播带货模式不断创新发展，通过结合社交媒体和电商平台，为消费者提供更加互动化的购物体验。',
      '共享经济模式在快速发展的同时也面临新的挑战，需要在创新商业模式和规范市场秩序之间找到平衡。',
      '绿色科技正在推动可持续发展，通过技术创新减少环境影响，为构建绿色低碳的未来社会贡献力量。'
    ]

    return Array.from({ length: count }, (_, index) => {
      const id = startId + index
      const titleIndex = (id - 1) % titles.length
      const sourceIndex = (id - 1) % sources.length
      const categoryIndex = (id - 1) % mockCategories.length

      return {
        id: id.toString(),
        title: titles[titleIndex],
        summary: summaries[titleIndex],
        content: '完整文章内容...',
        source: sources[sourceIndex],
        url: `https://example.com/news/${id}`,
        publishedAt: new Date(Date.now() - (id - 1) * 3600000).toISOString(), // 每篇文章间隔1小时
        tags: [mockCategories[categoryIndex], categoryIndex < 2 ? '科技' : '商业'],
        readTime: Math.floor(Math.random() * 5) + 2, // 2-6分钟
        rating: Math.round((Math.random() * 2 + 3.5) * 10) / 10, // 3.5-5.5分
        isRead: Math.random() < 0.2 // 20%已读
      }
    })
  }

  // 加载更多数据 - 使用useCallback避免不必要的重新创建
  const loadMoreNews = useCallback(async () => {
    if (loadingMore || !hasMore) return

    setLoadingMore(true)
    await fetchNews(page + 1, true)
    setLoadingMore(false)
  }, [loadingMore, hasMore, page, fetchNews])

  // 监听源类型变化，获取个人订阅源
  useEffect(() => {
    fetchPersonalFeeds()
  }, [fetchPersonalFeeds])

  // 初始加载数据
  useEffect(() => {
    const loadInitialData = async () => {
      // 只在没有数据时显示loading
      if (news.length === 0) {
        setLoading(true)
      }
      await Promise.all([
        fetchNews(1, false),
        fetchUserSubscription()
      ])
      setLoading(false)
    }

    loadInitialData()
  }, [fetchNews, fetchUserSubscription]) // 依赖fetchNews和fetchUserSubscription

  // 根据用户状态调整评分限制
  useEffect(() => {
    if (!userSubscription.is_pro && minRating > 3.9) {
      setMinRating(3.9)
    }
  }, [userSubscription.is_pro, minRating])

  // 处理登录状态变化时的源类型重置
  useEffect(() => {
    if (!session && selectedSourceType === '个人订阅') {
      setSelectedSourceType('全部')
    }
  }, [session, selectedSourceType])

  // 右侧新闻列表的无限滚动监听
  const handleNewsListScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    const target = e.target as HTMLDivElement
    if (target.scrollTop + target.clientHeight >= target.scrollHeight - 1000) {
      loadMoreNews()
    }
  }, [loadMoreNews])

  // 使用useMemo缓存筛选结果，避免不必要的重新计算
  const filteredNews = useMemo(() => {
    return news.filter(item => {
      // 评分筛选
      if (onlyRated) {
        const score = item.aiScore || item.rating || 0
        if (score < minRating) return false
      }

      // 分类筛选
      if (selectedCategory !== '全部') {
        if (!item.tags.includes(selectedCategory)) return false
      }

      // 源类型筛选 - 现在需要在客户端处理
      if (selectedSourceType !== '全部') {
        // 这里可以根据实际的源类型逻辑进行筛选
        // 暂时跳过，因为模拟数据没有源类型字段
      }

      // 个人订阅源筛选
      if (selectedSourceType === '个人订阅' && selectedFeed !== '全部') {
        // 这里可以根据实际的订阅源逻辑进行筛选
        // 暂时跳过，因为模拟数据没有订阅源字段
      }

      return true
    })
  }, [news, onlyRated, minRating, selectedCategory, selectedSourceType, selectedFeed])

  const handleMarkAsRead = (newsId: string) => {
    setNews(prev => prev.map(item =>
      item.id === newsId ? { ...item, isRead: true } : item
    ))
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))
    const diffInHours = Math.floor(diffInMinutes / 60)
    const diffInDays = Math.floor(diffInHours / 24)

    if (diffInMinutes < 1) return '刚刚'
    if (diffInMinutes < 60) return `${diffInMinutes}分钟前`
    if (diffInHours < 24) return `${diffInHours}小时前`
    if (diffInDays < 7) return `${diffInDays}天前`
    return date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' })
  }

  // 如果正在加载且没有数据，显示loading界面
  if (loading && news.length === 0) {
    return (
      <div className="h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 overflow-hidden">
        <Navigation />
        <div className="h-[calc(100vh-4rem)] bg-gray-50 flex items-center justify-center">
          <div className="bg-white border-2 border-black p-8 shadow-[4px_4px_0px_0px_rgba(0,0,0,1)]">
            <div className="flex items-center space-x-4">
              <div className="w-6 h-6 border-2 border-black border-t-transparent animate-spin"></div>
              <span className="font-mono font-bold text-black">LOADING NEWS...</span>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 overflow-hidden">
      <Navigation />
      <style jsx>{`
        .slider::-webkit-slider-thumb {
          appearance: none;
          height: 20px;
          width: 20px;
          border-radius: 0;
          background: #000000;
          cursor: pointer;
          box-shadow: 2px 2px 0px rgba(0,0,0,0.3);
        }
        .slider::-moz-range-thumb {
          height: 20px;
          width: 20px;
          border-radius: 0;
          background: #000000;
          cursor: pointer;
          border: 2px solid #000000;
          box-shadow: 2px 2px 0px rgba(0,0,0,0.3);
        }
        .ticket-perforation {
          background-image: radial-gradient(circle at 10px 10px, transparent 3px, white 3px);
          background-size: 20px 20px;
        }
      `}</style>
      <div className="h-[calc(100vh-4rem)] bg-gray-50 flex overflow-hidden">
        {/* 左侧筛选栏 - 极度紧凑，独立滚动 */}
        <div className="w-72 flex-shrink-0 bg-gray-50 border-r border-gray-200 overflow-y-auto">
            <div className="p-2 space-y-2">

            {/* 订阅状态 - 只有登录用户才显示 */}
            {session && <SubscriptionStatus />}

            {/* 未登录提示 */}
            {!session && (
              <div className="bg-yellow-100 border border-yellow-400 shadow-[2px_2px_0px_0px_rgba(0,0,0,1)] mb-2">
                <div className="bg-yellow-400 text-black p-2 flex items-center justify-between">
                  <div>
                    <h3 className="text-base font-bold tracking-wider font-mono">LOGIN</h3>
                    <p className="text-xs opacity-80">登录 / SIGN IN</p>
                  </div>
                  <div className="text-base">🔑</div>
                </div>
                <div className="p-3 text-center">
                  <p className="text-sm text-gray-700 mb-3">
                    登录后解锁更多功能
                  </p>
                  <div className="space-y-2">
                    <Link
                      href="/auth/signin"
                      className="w-full bg-black text-white px-4 py-2 font-mono font-bold text-sm hover:bg-gray-800 transition-all border border-black shadow-[1px_1px_0px_0px_rgba(0,0,0,0.3)] block text-center"
                    >
                      立即登录
                    </Link>
                    <Link
                      href="/auth/signup"
                      className="w-full bg-white text-black px-4 py-2 font-mono font-bold text-sm hover:bg-gray-100 transition-all border border-black shadow-[1px_1px_0px_0px_rgba(0,0,0,0.3)] block text-center"
                    >
                      免费注册
                    </Link>
                  </div>
                </div>
              </div>
            )}

            {/* 评分筛选 */}
            <div className="bg-white border border-black shadow-[2px_2px_0px_0px_rgba(0,0,0,1)]">
              <div className="bg-black text-white p-2 flex items-center justify-between">
                <div>
                  <h3 className="text-base font-bold tracking-wider font-mono">RATING</h3>
                  <p className="text-xs opacity-80">评分 / FILTER</p>
                </div>
                <div className="text-base">★</div>
              </div>
              <div className="p-1.5">
                {/* 仅看已评分勾选项 */}
                <div className="mb-3">
                  <label className="flex items-center space-x-3 cursor-pointer">
                    <input
                      type="checkbox"
                      checked={onlyRated}
                      onChange={(e) => setOnlyRated(e.target.checked)}
                      className="w-4 h-4 border-2 border-black bg-white checked:bg-black checked:border-black focus:ring-0 focus:ring-offset-0"
                    />
                    <span className="text-sm font-mono font-bold tracking-wide">仅看已评分</span>
                  </label>
                </div>

                {/* 评分滑块 - 只有勾选时才启用，根据会员状态限制最大值 */}
                <div className={`${onlyRated ? '' : 'opacity-50 pointer-events-none'}`}>
                  <div className="text-center mb-3">
                    <div className="text-xs text-gray-600 font-mono tracking-wide mb-2">MINIMUM RATING</div>
                    <div className="text-3xl font-bold text-black font-mono">{minRating}</div>
                    <div className="text-xs text-gray-500 mt-1">
                      最低评分 {!userSubscription.is_pro && <span className="text-red-500">(最高3.9★)</span>}
                    </div>
                  </div>
                  <div className="relative">
                    <input
                      type="range"
                      min="1"
                      max={userSubscription.is_pro ? "5" : "3.9"}
                      step="0.1"
                      value={Math.min(minRating, userSubscription.is_pro ? 5 : 3.9)}
                      onChange={(e) => {
                        const value = parseFloat(e.target.value)
                        const maxValue = userSubscription.is_pro ? 5 : 3.9
                        setMinRating(Math.min(value, maxValue))
                      }}
                      disabled={!onlyRated}
                      className="w-full h-2 bg-gray-200 appearance-none cursor-pointer slider border border-black disabled:cursor-not-allowed"
                    />
                    <div className="flex justify-between text-xs text-gray-500 mt-2 font-mono">
                      <span>1.0</span>
                      <span>2.5</span>
                      <span className={userSubscription.is_pro ? '' : 'text-red-500 font-bold'}>
                        {userSubscription.is_pro ? '5.0' : '3.9'}
                      </span>
                    </div>
                    {!userSubscription.is_pro && (
                      <div className="text-xs text-red-500 mt-2 text-center font-mono">
                        <Link href="/pricing" className="hover:underline">
                          升级Pro解锁5.0★筛选
                        </Link>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* 分类筛选 */}
            <div className="bg-white border border-black shadow-[2px_2px_0px_0px_rgba(0,0,0,1)]">
              <div className="bg-black text-white p-2 flex items-center justify-between">
                <div>
                  <h3 className="text-base font-bold tracking-wider font-mono">CATEGORY</h3>
                  <p className="text-xs opacity-80">分类 / FILTER</p>
                </div>
                <div className="text-base">#</div>
              </div>
              <div className="p-1.5">
                <div className="grid grid-cols-4 gap-1">
                  {categories.map((category, index) => (
                    <button
                      key={category}
                      onClick={() => setSelectedCategory(category)}
                      className={`text-center px-1 py-1 border font-mono text-xs font-bold tracking-wide transition-all whitespace-nowrap ${
                        selectedCategory === category
                          ? 'bg-black text-white border-black shadow-[1px_1px_0px_0px_rgba(0,0,0,0.3)]'
                          : 'bg-white text-black border-gray-300 hover:border-black hover:shadow-[1px_1px_0px_0px_rgba(0,0,0,0.1)]'
                      }`}
                    >
                      {category}
                    </button>
                  ))}
                </div>
              </div>
            </div>

            {/* 源类型筛选 - 只有登录用户才能使用个人订阅 */}
            <div className="bg-white border border-black shadow-[2px_2px_0px_0px_rgba(0,0,0,1)]">
              <div className="bg-black text-white p-2 flex items-center justify-between">
                <div>
                  <h3 className="text-base font-bold tracking-wider font-mono">SOURCE</h3>
                  <p className="text-xs opacity-80">来源 / TYPE</p>
                </div>
                <div className="text-base">@</div>
              </div>
              <div className="p-1.5">
                <div className="space-y-1">
                  {sourceTypes.map((sourceType, index) => {
                    // 如果未登录且选择个人订阅，显示提示
                    const isDisabled = !session && sourceType === '个人订阅'

                    return (
                      <button
                        key={sourceType}
                        onClick={() => {
                          if (isDisabled) return
                          setSelectedSourceType(sourceType)
                          // 切换源类型时，重置feed选择
                          setSelectedFeed('全部')
                        }}
                        disabled={isDisabled}
                        className={`w-full text-left px-2 py-1 border font-mono text-sm font-bold tracking-wide transition-all ${
                          selectedSourceType === sourceType
                            ? 'bg-black text-white border-black shadow-[1px_1px_0px_0px_rgba(0,0,0,0.3)]'
                            : isDisabled
                            ? 'bg-gray-100 text-gray-400 border-gray-200 cursor-not-allowed'
                            : 'bg-white text-black border-gray-300 hover:border-black hover:shadow-[1px_1px_0px_0px_rgba(0,0,0,0.1)]'
                        }`}
                      >
                        <span className="text-xs text-gray-500 mr-2">#{String(index + 1).padStart(2, '0')}</span>
                        {sourceType}
                        {isDisabled && <span className="text-xs text-red-500 ml-2">(需登录)</span>}
                      </button>
                    )
                  })}
                </div>
              </div>
            </div>

            {/* 个人订阅源筛选 - 只在选择个人订阅且已登录时显示 */}
            {selectedSourceType === '个人订阅' && session && (
              <div className="bg-white border border-black shadow-[2px_2px_0px_0px_rgba(0,0,0,1)]">
                <div className="bg-black text-white p-2 flex items-center justify-between">
                  <div>
                    <h3 className="text-base font-bold tracking-wider font-mono">FEEDS</h3>
                    <p className="text-xs opacity-80">订阅源 / SUBSCRIPTION</p>
                  </div>
                  <div className="text-base">📰</div>
                </div>
                <div className="p-1.5">
                  {feedsLoading ? (
                    <div className="text-center py-4">
                      <div className="text-sm text-gray-500">加载订阅源中...</div>
                    </div>
                  ) : (
                    <div className="space-y-2">
                      {personalFeeds.map((feed, index) => (
                        <button
                          key={feed.id}
                          onClick={() => setSelectedFeed(feed.id)}
                          className={`w-full text-left px-3 py-2 border font-mono text-sm font-bold tracking-wide transition-all ${
                            selectedFeed === feed.id
                              ? 'bg-black text-white border-black shadow-[1px_1px_0px_0px_rgba(0,0,0,0.3)]'
                              : 'bg-white text-black border-gray-300 hover:border-black hover:shadow-[1px_1px_0px_0px_rgba(0,0,0,0.1)]'
                          }`}
                        >
                          <span className="text-xs text-gray-500 mr-2">#{String(index + 1).padStart(2, '0')}</span>
                          {feed.title}
                        </button>
                      ))}
                      {personalFeeds.length === 1 && (
                        <div className="text-center py-4">
                          <div className="text-sm text-gray-500">暂无个人订阅源</div>
                          <Link
                            href="/my-subscriptions"
                            className="text-xs text-blue-600 hover:text-blue-800 underline mt-1 inline-block"
                          >
                            前往我的订阅页面添加
                          </Link>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>
            )}
            </div>
          </div>

        {/* 右侧新闻列表 - 独立滚动 */}
        <div className="flex-1 overflow-y-auto bg-gray-50" onScroll={handleNewsListScroll}>
            <div className="p-3 space-y-3">
              {filteredNews.map((item, index) => {
                // 判断是否应该锁定
                const isLocked = isNewsLocked(item)
                // 判断是否为高分内容（≥4.0）
                const isHighScore = (item.aiScore || item.rating || 0) >= 4.0
                // 判断是否为付费用户解锁的高分内容
                const isPremiumUnlocked = isHighScore && userSubscription.is_pro && !isLocked

                // 如果锁定，使用LockedNewsCard
                if (isLocked) {
                  return (
                    <LockedNewsCard
                      key={item.id}
                      id={item.id}
                      title={item.title}
                      summary={item.summary}
                      source={item.source}
                      publishedAt={item.publishedAt}
                      aiScore={item.aiScore || item.rating || 0}
                      tags={item.tags}
                      readTime={item.readTime}
                      isSystem={item.isSystem || false}
                      unlockMode="D+1"
                      onUpgrade={() => {
                        window.location.href = '/pricing'
                      }}
                      onTrial={() => {
                        window.location.href = '/pricing'
                      }}
                    />
                  )
                }

                // 如果是付费用户解锁的高分内容，使用PremiumNewsCard
                if (isPremiumUnlocked) {
                  return (
                    <PremiumNewsCard
                      key={item.id}
                      id={item.id}
                      title={item.title}
                      summary={item.summary}
                      source={item.source}
                      publishedAt={item.publishedAt}
                      aiScore={item.aiScore || item.rating || 0}
                      tags={item.tags}
                      readTime={item.readTime}
                      isSystem={item.isSystem || false}
                      isRead={item.isRead}
                      onMarkAsRead={handleMarkAsRead}
                    />
                  )
                }

                // 否则使用普通卡片
                return (
                <article
                  key={item.id}
                  className={`bg-white border border-black shadow-[3px_3px_0px_0px_rgba(0,0,0,1)] hover:shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] transition-all duration-300 ${
                    item.isRead ? 'opacity-60' : ''
                  }`}
                >
                  {/* 票券头部 */}
                  <div className="bg-black text-white p-2 flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="bg-white text-black px-2 py-1 font-mono text-xs font-bold">
                        #{String(index + 1).padStart(3, '0')}
                      </div>
                      <div className="border-l border-gray-400 pl-3">
                        <div className="text-xs font-mono opacity-80">SOURCE</div>
                        <div className="text-sm font-bold tracking-wide">{item.source}</div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-xs font-mono opacity-80">RATING</div>
                      <div className="text-lg font-bold">★ {item.aiScore || item.rating}</div>
                    </div>
                  </div>

                  {/* 票券主体 */}
                  <div className="p-3">
                    {/* 时间戳和状态 */}
                    <div className="flex items-center justify-between mb-3 pb-2 border-b border-gray-200">
                      <div className="flex items-center space-x-3">
                        <div className="text-xs font-mono text-gray-500">
                          {formatDate(item.publishedAt)}
                        </div>
                        <div className="text-xs font-mono text-gray-500">
                          {item.readTime} MIN READ
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        {item.isRead && (
                          <span className="bg-gray-200 text-gray-700 px-2 py-1 text-xs font-mono font-bold">
                            READ
                          </span>
                        )}
                      </div>
                    </div>

                    {/* 标题区域 */}
                    <Link
                      href={`/news/${item.id}`}
                      className="block hover:text-gray-600 transition-colors mb-3"
                      onClick={() => handleMarkAsRead(item.id)}
                    >
                      <h2 className="text-xl font-bold text-black mb-1 line-clamp-2 leading-tight">
                        {item.title}
                      </h2>
                      <div className="w-12 h-0.5 bg-black"></div>
                    </Link>

                    {/* 摘要 */}
                    <p className="text-gray-700 mb-3 line-clamp-2 leading-relaxed text-sm">
                      {item.summary}
                    </p>

                    {/* 标签区域 */}
                    <div className="flex flex-wrap gap-2 mb-3">
                      {item.tags.map((tag, tagIndex) => (
                        <span
                          key={tag}
                          className="px-2 py-1 bg-gray-100 text-black text-xs font-mono font-bold border border-gray-300"
                        >
                          #{tagIndex + 1} {tag}
                        </span>
                      ))}
                    </div>

                    {/* 操作区域 */}
                    <div className="border-t border-dashed border-gray-300 pt-3">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <button
                            onClick={(e) => {
                              e.preventDefault()
                              handleMarkAsRead(item.id)
                              window.open(`/news/${item.id}`, '_blank', 'noopener,noreferrer')
                            }}
                            className="bg-black text-white px-4 py-2 font-mono text-sm font-bold hover:bg-gray-800 transition-all border border-black shadow-[1px_1px_0px_0px_rgba(0,0,0,0.3)] cursor-pointer"
                          >
                            READ FULL →
                          </button>

                          <button className="flex items-center space-x-1 text-gray-600 hover:text-black px-3 py-2 border border-gray-300 hover:border-black transition-all font-mono text-sm font-bold">
                            <span>↗</span>
                            <span>SHARE</span>
                          </button>
                        </div>
                        <div className="flex items-center space-x-3 text-sm text-gray-500 font-mono">
                          <div className="text-right">
                            <div className="text-xs">READ TIME</div>
                            <div className="font-bold text-black text-sm">{item.readTime} MIN</div>
                          </div>
                          <a
                            href={item.url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-gray-500 hover:text-black transition-colors"
                            title="查看原文"
                          >
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                            </svg>
                          </a>
                        </div>
                      </div>
                    </div>
                  </div>
                </article>
                )
              })}

              {/* 加载更多指示器 */}
              {filteredNews.length > 0 && (
                <div className="text-center py-12">
                  {loadingMore && (
                    <div className="bg-white border-2 border-black p-8 shadow-[4px_4px_0px_0px_rgba(0,0,0,1)]">
                      <div className="flex items-center justify-center space-x-4">
                        <div className="w-6 h-6 border-2 border-black border-t-transparent animate-spin"></div>
                        <span className="font-mono font-bold text-black">LOADING...</span>
                      </div>
                    </div>
                  )}

                  {!hasMore && !loadingMore && filteredNews.length > 0 && (
                    <div className="bg-white border-2 border-black p-8 shadow-[4px_4px_0px_0px_rgba(0,0,0,1)]">
                      <div className="text-4xl mb-4">■</div>
                      <p className="font-mono font-bold text-black">END OF CONTENT</p>
                      <p className="text-sm text-gray-600 mt-2">已加载全部内容</p>
                    </div>
                  )}
                </div>
              )}

              {filteredNews.length === 0 && !loading && (
                <div className="text-center py-20">
                  <div className="bg-white border-2 border-black p-12 shadow-[6px_6px_0px_0px_rgba(0,0,0,1)] max-w-md mx-auto">
                    <div className="text-6xl mb-6">□</div>
                    <h3 className="text-2xl font-bold text-black mb-4 font-mono">NO CONTENT</h3>
                    <p className="text-gray-600 leading-relaxed">
                      {(() => {
                        let message = `暂时没有评分 ${minRating} 以上的新闻`

                        if (selectedCategory !== '全部' && selectedSourceType !== '全部') {
                          message = `暂时没有"${selectedCategory}"分类且来自"${selectedSourceType}"的新闻`
                        } else if (selectedCategory !== '全部') {
                          message = `暂时没有"${selectedCategory}"相关且评分 ${minRating} 以上的新闻`
                        } else if (selectedSourceType !== '全部') {
                          message = `暂时没有来自"${selectedSourceType}"且评分 ${minRating} 以上的新闻`
                        }

                        return message
                      })()}
                    </p>
                    <div className="mt-6 pt-6 border-t border-gray-300">
                      <p className="text-xs font-mono text-gray-500">TRY ADJUSTING FILTERS</p>
                    </div>
                  </div>
                </div>
              )}
          </div>
        </div>
      </div>
    </div>
  )
}
