'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import Navigation from '@/components/Navigation'
import AddSubscriptionModal from '@/components/AddSubscriptionModal'

interface Subscription {
  id: string
  name: string
  url: string
  description: string
  category: string
  tags: string[]
  articleCount: number
  todayCount: number
  rating: number
  lastUpdate: string
  status: 'active' | 'error' | 'paused'
}

export default function SubscriptionsPage() {
  const { data: session } = useSession()
  const [subscriptions, setSubscriptions] = useState<Subscription[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('全部')
  const [isAddSubscriptionModalOpen, setIsAddSubscriptionModalOpen] = useState(false)
  const [notification, setNotification] = useState<{
    message: string
    type: 'success' | 'error'
  } | null>(null)
  const [stats, setStats] = useState({
    totalSources: 0,
    activeSources: 0,
    totalArticles: 0,
    categoryBreakdown: {} as Record<string, number>
  })

  // 处理添加订阅按钮点击
  const handleAddSubscriptionClick = () => {
    if (!session) {
      // 保存当前页面，登录后回来
      sessionStorage.setItem('redirectAfterLogin', '/subscriptions')
      window.location.href = '/auth/signin'
      return
    }
    setIsAddSubscriptionModalOpen(true)
  }

  const categories = ['全部', '科技', '财经', 'AI', '创业', '生活', '教育', '娱乐']



  // API调用函数 - 获取用户的Miniflux订阅
  const fetchUserSubscriptions = async () => {
    try {
      setLoading(true)

      const response = await fetch('/api/subscription')
      if (!response.ok) {
        throw new Error('Failed to fetch user subscriptions')
      }

      const data = await response.json()

      if (data.success && data.feeds) {
        // 转换Miniflux数据格式以匹配现有的Subscription接口
        const convertedSources = data.feeds.map((feed: any) => ({
          id: feed.id.toString(),
          name: feed.title,
          url: feed.feed_url,
          description: feed.site_url || '暂无描述',
          category: feed.category?.title || '默认分类',
          tags: [feed.category?.title || '默认分类'],
          articleCount: feed.unread_count || 0,
          todayCount: Math.floor(Math.random() * 30) + 5, // 模拟今日文章数
          rating: Math.round((Math.random() * 1 + 4) * 10) / 10, // 4.0-5.0评分
          lastUpdate: formatLastUpdate(feed.checked_at || new Date().toISOString()),
          status: feed.disabled ? 'paused' : 'active'
        }))

        setSubscriptions(convertedSources)

        // 生成统计数据
        const totalSources = convertedSources.length
        const activeSources = convertedSources.filter((s: any) => s.status === 'active').length
        const totalArticles = convertedSources.reduce((sum: number, s: any) => sum + s.articleCount, 0)

        setStats({
          totalSources,
          activeSources,
          totalArticles,
          categoryBreakdown: convertedSources.reduce((acc: Record<string, number>, source: any) => {
            acc[source.category] = (acc[source.category] || 0) + 1
            return acc
          }, {})
        })
      } else {
        console.error('获取订阅失败:', data.error)
        showNotification('获取订阅失败: ' + (data.error || '未知错误'), 'error')
      }

    } catch (error) {
      console.error('获取用户订阅失败:', error)
      showNotification('获取订阅失败，请检查网络连接', 'error')
    } finally {
      setLoading(false)
    }
  }

  // 格式化最后更新时间
  const formatLastUpdate = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))

    if (diffInMinutes < 60) {
      return `${diffInMinutes}分钟前`
    } else if (diffInMinutes < 1440) {
      return `${Math.floor(diffInMinutes / 60)}小时前`
    } else {
      return `${Math.floor(diffInMinutes / 1440)}天前`
    }
  }

  // 加载用户订阅源
  useEffect(() => {
    fetchUserSubscriptions()
  }, [selectedCategory])

  // 备用模拟数据
  useEffect(() => {
    if (subscriptions.length === 0 && !loading) {
      const mockSubscriptions: Subscription[] = [
      {
        id: '1',
        name: '36氪',
        url: 'https://36kr.com/feed',
        description: '中国领先的科技媒体',
        category: '科技',
        tags: ['科技', '创业'],
        articleCount: 156,
        todayCount: 24,
        rating: 4.6,
        lastUpdate: '2小时前',
        status: 'active'
      },
      {
        id: '2',
        name: 'TechCrunch',
        url: 'https://techcrunch.com/feed/',
        description: '全球科技创业资讯',
        category: '科技',
        tags: ['科技', 'AI', '硬件'],
        articleCount: 203,
        todayCount: 18,
        rating: 4.8,
        lastUpdate: '1小时前',
        status: 'active'
      },
      {
        id: '3',
        name: 'Hacker News',
        url: 'https://hnrss.org/frontpage',
        description: '程序员社区热门讨论',
        category: '技术',
        tags: ['开源', '编程'],
        articleCount: 89,
        todayCount: 32,
        rating: 4.4,
        lastUpdate: '30分钟前',
        status: 'active'
      },
      {
        id: '4',
        name: 'AI研习社',
        url: 'https://ai.yanxishe.com/feed',
        description: '人工智能前沿资讯',
        category: 'AI',
        tags: ['机器学习', '深度学习'],
        articleCount: 67,
        todayCount: 15,
        rating: 4.7,
        lastUpdate: '1小时前',
        status: 'active'
      },
      {
        id: '5',
        name: '知乎日报',
        url: 'https://rsshub.app/zhihu/daily',
        description: '每日精选优质内容',
        category: '生活',
        tags: ['生活', '文化'],
        articleCount: 45,
        todayCount: 8,
        rating: 4.3,
        lastUpdate: '3小时前',
        status: 'active'
      },
      {
        id: '6',
        name: '财新网',
        url: 'https://caixin.com/feed',
        description: '专业财经新闻',
        category: '财经',
        tags: ['财经', '政策'],
        articleCount: 78,
        todayCount: 12,
        rating: 4.5,
        lastUpdate: '2小时前',
        status: 'active'
      }
      ]
      setSubscriptions(mockSubscriptions)
    }
  }, [subscriptions.length, loading])

  const toggleSubscription = async (id: string) => {
    try {
      // 找到当前订阅的状态
      const currentSub = subscriptions.find(sub => sub.id === id)
      if (!currentSub) {
        showNotification('订阅不存在', 'error')
        return
      }

      const newDisabled = currentSub.status === 'active'

      const response = await fetch(`/api/subscription/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          disabled: newDisabled
        })
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || '更新订阅状态失败')
      }

      if (data.success) {
        // 更新本地状态
        setSubscriptions(prev => prev.map(sub =>
          sub.id === id ? { ...sub, status: newDisabled ? 'paused' : 'active' } : sub
        ))
        showNotification(data.message || '订阅状态已更新', 'success')
      } else {
        throw new Error(data.error || '更新订阅状态失败')
      }
    } catch (error: any) {
      console.error('更新订阅状态失败:', error)
      showNotification('更新订阅状态失败: ' + (error.message || '未知错误'), 'error')
    }
  }

  const removeSubscription = async (id: string) => {
    try {
      const response = await fetch(`/api/subscription/${id}`, {
        method: 'DELETE'
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || '删除订阅失败')
      }

      if (data.success) {
        // 从本地状态中移除
        setSubscriptions(prev => prev.filter(sub => sub.id !== id))
        showNotification('订阅已删除', 'success')

        // 重新获取订阅列表以确保同步
        fetchUserSubscriptions()
      } else {
        throw new Error(data.error || '删除订阅失败')
      }
    } catch (error: any) {
      console.error('删除订阅失败:', error)
      showNotification('删除订阅失败: ' + (error.message || '未知错误'), 'error')
    }
  }

  const addSubscription = (newSub: Omit<Subscription, 'id' | 'lastUpdate' | 'articleCount' | 'todayCount' | 'rating' | 'status' | 'tags'>) => {
    const subscription: Subscription = {
      ...newSub,
      id: Date.now().toString(),
      lastUpdate: '刚刚',
      articleCount: 0,
      todayCount: 0,
      rating: 0,
      status: 'active',
      tags: [newSub.category]
    }
    setSubscriptions(prev => [...prev, subscription])
    showNotification(`已成功添加订阅：${subscription.name}`, 'success')
  }

  // 直接添加推荐源
  const addRecommendedSource = async (source: { name: string; url: string; category: string }) => {
    if (!session) {
      // 保存当前页面，登录后回来
      sessionStorage.setItem('redirectAfterLogin', '/subscriptions')
      window.location.href = '/auth/signin'
      return
    }

    try {
      setLoading(true)

      const response = await fetch('/api/subscription', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          feed_url: source.url,
          category_name: source.category,
          title: source.name
        })
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || '添加订阅失败')
      }

      if (data.success) {
        showNotification(`已成功添加订阅：${source.name}`, 'success')
        // 重新获取订阅列表
        fetchUserSubscriptions()
      } else {
        throw new Error(data.error || '添加订阅失败')
      }
    } catch (error: any) {
      console.error('添加推荐源失败:', error)
      showNotification('添加订阅失败: ' + (error.message || '未知错误'), 'error')
    } finally {
      setLoading(false)
    }
  }

  const showNotification = (message: string, type: 'success' | 'error') => {
    setNotification({ message, type })
    setTimeout(() => setNotification(null), 3000)
  }

  const isRSSUrl = (url: string) => {
    const rssPatterns = [
      /^https?:\/\/.+\.(xml|rss|atom)$/i,
      /^https?:\/\/.+\/feed\/?$/i,
      /^https?:\/\/.+\/rss\/?$/i,
      /^https?:\/\/.+\/atom\/?$/i,
      /rsshub\./i,
      /feeds\./i
    ]
    return rssPatterns.some(pattern => pattern.test(url))
  }

  const filteredSubscriptions = subscriptions.filter(sub => {
    // 分类筛选
    if (selectedCategory !== '全部' && sub.category !== selectedCategory) {
      return false
    }
    
    // 搜索筛选
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      return (
        sub.name.toLowerCase().includes(query) ||
        sub.description.toLowerCase().includes(query) ||
        sub.category.toLowerCase().includes(query) ||
        sub.tags.some(tag => tag.toLowerCase().includes(query))
      )
    }
    
    return true
  })

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      <Navigation />
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* 添加订阅按钮 */}
          <div className="text-center mb-8">
            <button
              onClick={handleAddSubscriptionClick}
              className="px-8 py-4 border-2 font-mono font-bold transition-all flex items-center space-x-3 mx-auto bg-black text-white border-black hover:bg-gray-800 shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] hover:shadow-[6px_6px_0px_0px_rgba(0,0,0,1)]"
            >
              <span className="text-xl">+</span>
              <span>ADD SUBSCRIPTION</span>
            </button>
          </div>



          {/* 搜索和筛选区域 */}
          <div className="bg-white border border-black shadow-[3px_3px_0px_0px_rgba(0,0,0,1)] p-4 mb-6">
            <div className="relative mb-4">
              <input
                type="text"
                placeholder="SEARCH SOURCES OR ENTER RSS URL..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full px-4 py-2 pr-14 border border-gray-300 bg-white text-sm placeholder-gray-500 focus:outline-none focus:border-black transition-all font-mono"
              />
              <button className="absolute right-3 top-1/2 transform -translate-y-1/2 text-black hover:text-gray-600 transition-colors">
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
                </svg>
              </button>
              {isRSSUrl(searchQuery) && (
                <button
                  onClick={handleAddSubscriptionClick}
                  className="absolute right-12 top-1/2 transform -translate-y-1/2 bg-black text-white px-2 py-1 text-xs hover:bg-gray-800 transition-colors font-mono font-bold"
                >
                  SUBSCRIBE
                </button>
              )}
            </div>

            <div className="flex flex-wrap gap-2 justify-center">
              {categories.map((category) => (
                <button
                  key={category}
                  onClick={() => setSelectedCategory(category)}
                  className={`px-3 py-1.5 text-xs font-mono font-bold transition-all duration-300 border ${
                    selectedCategory === category
                      ? 'bg-black text-white border-black shadow-[1px_1px_0px_0px_rgba(0,0,0,0.3)]'
                      : 'bg-white text-black border-gray-300 hover:border-black hover:shadow-[1px_1px_0px_0px_rgba(0,0,0,0.1)]'
                  }`}
                >
                  {category}
                </button>
              ))}
            </div>
          </div>

          {/* 通知 */}
          {notification && (
            <div className={`fixed top-4 right-4 z-50 px-6 py-3 rounded-lg text-white font-medium ${
              notification.type === 'success' ? 'bg-green-500' : 'bg-red-500'
            }`}>
              {notification.message}
            </div>
          )}







          {/* 搜索结果区域 - 只有搜索时显示 */}
          {searchQuery && (
            <div className="bg-white border border-black shadow-[2px_2px_0px_0px_rgba(0,0,0,1)] mb-8">
              <div className="bg-black text-white p-3 flex items-center justify-between">
                <div>
                  <h2 className="text-lg font-bold tracking-wider font-mono">SEARCH RESULTS</h2>
                  <p className="text-xs opacity-80">搜索结果 / FOUND {filteredSubscriptions.length} SOURCES</p>
                </div>
                <div className="text-xl font-bold">🔍</div>
              </div>
              <div className="p-4">
                {filteredSubscriptions.length > 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                    {filteredSubscriptions.map((subscription, index) => (
                      <div
                        key={`search-${subscription.id}`}
                        className="bg-white border border-black shadow-[2px_2px_0px_0px_rgba(0,0,0,1)] hover:shadow-[3px_3px_0px_0px_rgba(0,0,0,1)] transition-all duration-300"
                      >
                        {/* 订阅源头部 */}
                        <div className="bg-black text-white p-2 flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            <div className="w-6 h-6 bg-white text-black flex items-center justify-center font-bold text-sm">
                              {subscription.name.charAt(0)}
                            </div>
                            <div>
                              <h3 className="text-sm font-bold tracking-wide">{subscription.name}</h3>
                            </div>
                          </div>
                          <div className={`w-2 h-2 ${
                            subscription.status === 'active' ? 'bg-green-400' : 'bg-gray-400'
                          }`}></div>
                        </div>

                        {/* 订阅源主体 */}
                        <div className="p-3">
                          <p className="text-xs text-gray-600 mb-2 leading-relaxed line-clamp-2">{subscription.description}</p>

                          {/* 统计数据 */}
                          <div className="bg-gray-100 border border-gray-300 p-2 mb-3">
                            <div className="grid grid-cols-3 gap-2 text-center">
                              <div className="border-r border-gray-300">
                                <div className="text-lg font-bold text-black font-mono">{subscription.todayCount}</div>
                                <div className="text-xs text-gray-600 font-mono">TODAY</div>
                              </div>
                              <div className="border-r border-gray-300">
                                <div className="text-lg font-bold text-black font-mono">{subscription.rating}</div>
                                <div className="text-xs text-gray-600 font-mono">RATING</div>
                              </div>
                              <div>
                                <div className="text-lg font-bold text-black font-mono">{subscription.articleCount}</div>
                                <div className="text-xs text-gray-600 font-mono">TOTAL</div>
                              </div>
                            </div>
                          </div>

                          {/* 标签 */}
                          <div className="flex flex-wrap gap-1 mb-3">
                            {subscription.tags.map((tag, tagIndex) => (
                              <span
                                key={tagIndex}
                                className="px-2 py-1 bg-gray-100 text-black text-xs font-mono font-bold border border-gray-300"
                              >
                                {tag}
                              </span>
                            ))}
                          </div>

                          {/* 操作按钮 */}
                          <div className="flex space-x-2 mb-2">
                            <button
                              onClick={() => toggleSubscription(subscription.id)}
                              className={`flex-1 px-3 py-1.5 font-mono text-xs font-bold transition-all border ${
                                subscription.status === 'active'
                                  ? 'bg-gray-200 text-black border-gray-400 hover:bg-gray-300'
                                  : 'bg-black text-white border-black hover:bg-gray-800'
                              }`}
                            >
                              {subscription.status === 'active' ? 'PAUSE' : 'ACTIVATE'}
                            </button>
                            <button
                              onClick={() => removeSubscription(subscription.id)}
                              className="flex-1 px-3 py-1.5 bg-white text-red-600 border border-red-300 hover:border-red-500 hover:bg-red-50 font-mono text-xs font-bold transition-all"
                            >
                              DELETE
                            </button>
                          </div>

                          {/* 最后更新时间 */}
                          <div className="text-center text-xs text-gray-500 font-mono border-t border-gray-300 pt-2">
                            LAST: {subscription.lastUpdate}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <div className="text-4xl mb-4">□</div>
                    <h3 className="text-lg font-bold text-black font-mono mb-2">NO RESULTS FOUND</h3>
                    <p className="text-gray-600 font-mono text-sm">
                      没有找到匹配 "{searchQuery}" 的订阅源
                    </p>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Discovery区域 - 平台推荐源 */}
          <div className="bg-white border border-black shadow-[2px_2px_0px_0px_rgba(0,0,0,1)]">
            <div className="bg-black text-white p-3 flex items-center justify-between">
              <div>
                <h2 className="text-lg font-bold tracking-wider font-mono">DISCOVERY</h2>
                <p className="text-xs opacity-80">发现新源 / PLATFORM RECOMMENDATIONS</p>
              </div>
              <div className="text-xl font-bold">★</div>
            </div>
            <div className="p-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                {[
                  { name: '少数派', url: 'https://sspai.com/feed', category: '科技', subscribers: '12.3K', type: 'RSS' },
                  { name: '阮一峰的网络日志', url: 'http://www.ruanyifeng.com/blog/atom.xml', category: '科技', subscribers: '25.6K', type: '博客' },
                  { name: 'V2EX', url: 'https://www.v2ex.com/index.xml', category: '科技', subscribers: '15.2K', type: '社区' },
                  { name: '机器之心', url: 'https://www.jiqizhixin.com/rss', category: 'AI', subscribers: '18.7K', type: 'AI' },
                  { name: 'InfoQ', url: 'https://infoq.com/feed', category: '科技', subscribers: '22.1K', type: '技术' },
                  { name: '虎嗅网', url: 'https://huxiu.com/feed', category: '财经', subscribers: '8.9K', type: '商业' },
                  { name: '36氪', url: 'https://36kr.com/feed', category: '创业', subscribers: '16.5K', type: '创业' },
                  { name: '知乎日报', url: 'https://feeds.feedburner.com/zhihu-daily', category: '生活', subscribers: '28.9K', type: '日报' },
                  { name: '丁香园', url: 'https://www.dxy.com/feed', category: '教育', subscribers: '9.2K', type: '医疗' }
                ].map((source, index) => (
                  <div key={`discovery-${index}`} className="bg-gray-50 border border-gray-300 p-3 hover:border-black hover:shadow-[1px_1px_0px_0px_rgba(0,0,0,0.1)] transition-all cursor-pointer">
                    <div className="flex items-center space-x-2 mb-3">
                      <div className="w-6 h-6 bg-black text-white flex items-center justify-center font-bold text-xs">
                        {source.name.charAt(0)}
                      </div>
                      <div className="flex-1">
                        <div className="font-bold text-black text-sm">{source.name}</div>
                        <div className="text-xs text-gray-500 font-mono truncate">{source.url}</div>
                      </div>
                    </div>
                    <div className="flex justify-between items-center mb-3">
                      <span className="text-xs text-gray-600 font-mono">{source.subscribers} SUBS</span>
                      <span className="px-2 py-1 bg-gray-100 text-black border border-gray-300 text-xs font-mono font-bold">
                        {source.category}
                      </span>
                    </div>
                    <button
                      onClick={() => addRecommendedSource({
                        name: source.name,
                        url: source.url,
                        category: source.category
                      })}
                      disabled={loading}
                      className={`w-full py-2 text-sm font-mono font-bold transition-all border border-black ${
                        loading
                          ? 'bg-gray-400 text-white cursor-not-allowed'
                          : 'bg-black text-white hover:bg-gray-800'
                      }`}
                    >
                      {loading ? 'ADDING...' : 'SUBSCRIBE'}
                    </button>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* 添加订阅模态框 */}
          <AddSubscriptionModal
            isOpen={isAddSubscriptionModalOpen}
            onClose={() => setIsAddSubscriptionModalOpen(false)}
            onAdd={addSubscription}
            onSuccess={() => {
              // 重新获取订阅列表
              fetchUserSubscriptions()
            }}
          />
        </div>
      </div>
    </div>
  )
}
