'use client'

import { useState } from 'react'
import Link from 'next/link'
import { useSession } from 'next-auth/react'
import Navigation from '@/components/Navigation'
import PaymentModal from '@/components/PaymentModal'

interface PricingPlan {
  id: string
  name: string
  period: 'monthly' | 'yearly'
  price: {
    cn: { amount: number; currency: '¥' }
    us: { amount: number; currency: '$' }
  }
  originalPrice?: {
    cn: { amount: number; currency: '¥' }
    us: { amount: number; currency: '$' }
  }
  features: string[]
  popular?: boolean
}

const plans: PricingPlan[] = [
  {
    id: 'pro-monthly',
    name: '专业版',
    period: 'monthly',
    price: {
      cn: { amount: 19, currency: '¥' },
      us: { amount: 6.99, currency: '$' }
    },
    originalPrice: {
      cn: { amount: 38, currency: '¥' },
      us: { amount: 13.98, currency: '$' }
    },
    features: [
      '100个个人订阅源',
      '高分内容实时解锁',
      '评分筛选最高5.0★',
      '无限历史查看',
      '无广告体验',
      '优先客服支持'
    ]
  },
  {
    id: 'pro-yearly',
    name: '专业版',
    period: 'yearly',
    price: {
      cn: { amount: 128, currency: '¥' },
      us: { amount: 59, currency: '$' }
    },
    originalPrice: {
      cn: { amount: 256, currency: '¥' },
      us: { amount: 95.88, currency: '$' }
    },
    features: [
      '100个个人订阅源',
      '高分内容实时解锁',
      '评分筛选最高5.0★',
      '无限历史查看',
      '无广告体验',
      '优先客服支持',
      '年付享44%优惠'
    ],
    popular: true
  }
]

const freeFeatures = [
  '10个个人订阅源',
  '高分内容延迟解锁',
  '评分筛选最高3.9★',
  '近7天历史查看',
  '系统推荐内容'
]

export default function PricingPage() {
  const { data: session } = useSession()
  const [market, setMarket] = useState<'cn' | 'us'>('cn')
  const [isLoading, setIsLoading] = useState<string | null>(null)
  const [showPaymentModal, setShowPaymentModal] = useState(false)
  const [selectedPlan, setSelectedPlan] = useState<PricingPlan | null>(null)

  const handleSubscribe = async (planId: string) => {
    if (!session) {
      // 保存当前页面，登录后回来
      sessionStorage.setItem('redirectAfterLogin', '/pricing')
      window.location.href = '/auth/signin'
      return
    }

    setIsLoading(planId)
    // 找到选中的方案
    const plan = plans.find(p => p.id === planId)
    if (plan) {
      setSelectedPlan(plan)
      setShowPaymentModal(true)
    }
    setIsLoading(null)
  }

  const handlePaymentSuccess = () => {
    // 支付成功后回到首页
    alert('支付成功！您已升级为专业版用户')
    window.location.href = '/'
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      <Navigation />
      <div className="min-h-screen bg-gray-50 py-12">
        <div className="max-w-6xl mx-auto px-4">
          {/* 页面标题 */}
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold text-black mb-4 font-mono tracking-wider">
              PRICING PLANS
            </h1>
            <p className="text-lg text-gray-600 mb-8">
              选择适合您的专业新闻阅读方案
            </p>
            
            {/* 市场切换 */}
            <div className="inline-flex bg-white border-2 border-black shadow-[2px_2px_0px_0px_rgba(0,0,0,1)]">
              <button
                onClick={() => setMarket('cn')}
                className={`px-6 py-2 font-mono font-bold text-sm transition-all ${
                  market === 'cn'
                    ? 'bg-black text-white'
                    : 'bg-white text-black hover:bg-gray-100'
                }`}
              >
                中国 🇨🇳
              </button>
              <button
                onClick={() => setMarket('us')}
                className={`px-6 py-2 font-mono font-bold text-sm transition-all ${
                  market === 'us'
                    ? 'bg-black text-white'
                    : 'bg-white text-black hover:bg-gray-100'
                }`}
              >
                海外 🌍
              </button>
            </div>
          </div>

          {/* 定价卡片 */}
          <div className="grid md:grid-cols-3 gap-8 mb-12">
            {/* 免费版 */}
            <div className="bg-white border-2 border-gray-300 shadow-[3px_3px_0px_0px_rgba(0,0,0,0.1)] flex flex-col">
              <div className="bg-gray-100 text-black p-4 text-center">
                <h3 className="text-xl font-bold font-mono tracking-wider">FREE</h3>
                <p className="text-sm opacity-80">免费版</p>
              </div>
              <div className="p-6 flex flex-col flex-1">
                <div className="text-center mb-6">
                  <div className="text-4xl font-bold text-black font-mono">¥0</div>
                  <div className="text-sm text-gray-500 mt-1">永久免费</div>
                </div>
                <ul className="space-y-3 mb-6 flex-1">
                  {freeFeatures.map((feature, index) => (
                    <li key={index} className="flex items-center text-sm">
                      <span className="w-4 h-4 bg-gray-300 text-white text-xs flex items-center justify-center mr-3 font-mono">
                        ✓
                      </span>
                      {feature}
                    </li>
                  ))}
                </ul>
                <div className="mt-auto">
                  {session ? (
                    <button
                      disabled
                      className="w-full py-3 bg-gray-200 text-gray-500 font-mono font-bold text-sm cursor-not-allowed"
                    >
                      当前方案
                    </button>
                  ) : (
                    <Link
                      href="/auth/signup"
                      className="w-full py-3 bg-black text-white font-mono font-bold text-sm hover:bg-gray-800 transition-all shadow-[2px_2px_0px_0px_rgba(0,0,0,0.3)] text-center block"
                    >
                      免费注册
                    </Link>
                  )}

                  {/* 占位空间，保持与专业版按钮区域高度一致 */}
                  <div className="text-center mt-4">
                    <div className="text-sm text-transparent font-mono">
                      占位文字保持高度
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* 付费版卡片 */}
            {plans.map((plan) => (
              <div
                key={plan.id}
                className={`bg-white border-2 shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] relative flex flex-col ${
                  plan.popular ? 'border-black' : 'border-gray-400'
                }`}
              >
                {plan.popular && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <div className="bg-black text-white px-4 py-1 text-xs font-mono font-bold">
                      推荐方案
                    </div>
                  </div>
                )}
                
                <div className={`p-4 text-center ${
                  plan.popular ? 'bg-black text-white' : 'bg-gray-800 text-white'
                }`}>
                  <h3 className="text-xl font-bold font-mono tracking-wider">
                    PRO {plan.period === 'yearly' ? 'YEARLY' : 'MONTHLY'}
                  </h3>
                  <p className="text-sm opacity-80">
                    {plan.period === 'yearly' ? '年付专业版' : '月付专业版'}
                  </p>
                </div>
                
                <div className="p-6 flex flex-col flex-1">
                  {/* 折扣标签 */}
                  {plan.originalPrice && (
                    <div className="text-center mb-4">
                      <div className="inline-block bg-red-600 text-white px-3 py-1 text-xs font-bold font-mono rounded">
                        限时 {Math.round((1 - plan.price[market].amount / plan.originalPrice[market].amount) * 100)}% OFF
                      </div>
                    </div>
                  )}

                  <div className="text-center mb-6">
                    <div className="text-4xl font-bold text-black font-mono">
                      {plan.price[market].currency}{plan.price[market].amount}
                    </div>
                    <div className="text-sm text-gray-500 mt-1">
                      {plan.period === 'yearly' ? '/ 年' : '/ 月'}
                    </div>
                    {plan.originalPrice && (
                      <div className="text-sm text-gray-400 line-through mt-1">
                        原价 {plan.originalPrice[market].currency}{plan.originalPrice[market].amount}
                      </div>
                    )}
                    {plan.originalPrice && (
                      <div className="text-sm text-green-600 font-bold mt-1">
                        节省 {plan.originalPrice[market].currency}{plan.originalPrice[market].amount - plan.price[market].amount}
                      </div>
                    )}
                    {plan.period === 'yearly' && (
                      <div className="text-sm text-blue-600 font-bold mt-1">
                        相当于 {plan.price[market].currency}{(plan.price[market].amount / 12).toFixed(1)}/月
                      </div>
                    )}
                  </div>

                  <ul className="space-y-3 mb-6 flex-1">
                    {plan.features.map((feature, index) => (
                      <li key={index} className="flex items-center text-sm">
                        <span className="w-4 h-4 bg-black text-white text-xs flex items-center justify-center mr-3 font-mono">
                          ✓
                        </span>
                        {feature}
                      </li>
                    ))}
                  </ul>

                  <div className="mt-auto">
                    <button
                      onClick={() => handleSubscribe(plan.id)}
                      disabled={isLoading === plan.id}
                      className={`w-full py-3 font-mono font-bold text-sm transition-all ${
                        plan.popular
                          ? 'bg-black text-white hover:bg-gray-800 shadow-[2px_2px_0px_0px_rgba(0,0,0,0.3)]'
                          : 'bg-gray-800 text-white hover:bg-black shadow-[2px_2px_0px_0px_rgba(0,0,0,0.3)]'
                      } ${isLoading === plan.id ? 'opacity-50 cursor-not-allowed' : ''}`}
                    >
                      {isLoading === plan.id ? '处理中...' : '立即订阅'}
                    </button>

                    <div className="text-center mt-4">
                      <Link
                        href="/trial"
                        className="text-sm text-gray-600 hover:text-black underline font-mono"
                      >
                        或领取7天免费试用
                      </Link>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* 常见问题 */}
          <div className="bg-white border-2 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] p-8">
            <h2 className="text-2xl font-bold text-black mb-6 font-mono tracking-wider text-center">
              常见问题 FAQ
            </h2>
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h3 className="font-bold text-black mb-2">什么是高分内容延迟解锁？</h3>
                <p className="text-sm text-gray-600">
                  评分≥4.0★的重要新闻，免费用户需要等待次日才能查看完整内容，付费用户可以实时解锁。
                </p>
              </div>
              <div>
                <h3 className="font-bold text-black mb-2">试用期结束后会自动扣费吗？</h3>
                <p className="text-sm text-gray-600">
                  国内用户试用结束后自动回到免费版，不会扣费。海外用户可选择自动续订。
                </p>
              </div>
              <div>
                <h3 className="font-bold text-black mb-2">支持哪些支付方式？</h3>
                <p className="text-sm text-gray-600">
                  中国用户支持微信支付和支付宝，海外用户支持信用卡支付。
                </p>
              </div>
              <div>
                <h3 className="font-bold text-black mb-2">可以随时取消订阅吗？</h3>
                <p className="text-sm text-gray-600">
                  是的，您可以随时在设置页面取消订阅，取消后仍可使用到当前付费周期结束。
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 支付弹窗 */}
      {selectedPlan && (
        <PaymentModal
          isOpen={showPaymentModal}
          onClose={() => setShowPaymentModal(false)}
          onSuccess={handlePaymentSuccess}
          selectedPlan={selectedPlan}
          market={market}
        />
      )}
    </div>
  )
}
