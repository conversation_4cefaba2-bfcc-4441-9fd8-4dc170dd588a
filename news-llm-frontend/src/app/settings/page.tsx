'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import AuthLayout from '@/components/AuthLayout'

interface UserSettings {
  // 个人信息
  username: string
  email: string
  currentPassword: string
  newPassword: string
  confirmPassword: string

  // 界面设置
  language: 'zh' | 'en'
}

export default function SettingsPage() {
  const { data: session, status } = useSession()
  const [settings, setSettings] = useState<UserSettings>({
    // 个人信息
    username: '',
    email: '',
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',

    // 界面设置
    language: 'zh'
  })

  const [isSavingPassword, setIsSavingPassword] = useState(false)
  const [isSavingProfile, setIsSavingProfile] = useState(false)
  const [showPasswordFields, setShowPasswordFields] = useState(false)
  const [showProfileEdit, setShowProfileEdit] = useState(false)
  const [isLoading, setIsLoading] = useState(true)

  // 加载用户信息
  useEffect(() => {
    const loadUserProfile = async () => {
      if (status === 'loading') return

      if (!session) {
        setIsLoading(false)
        return
      }

      try {
        // 先设置session中的基本信息
        setSettings(prev => ({
          ...prev,
          username: session.user?.username || session.user?.name || '',
          email: session.user?.email || '',
          language: 'zh'
        }))

        // 尝试从API加载完整信息
        const response = await fetch('/api/user-profile')
        if (response.ok) {
          const data = await response.json()
          if (data.success) {
            setSettings(prev => ({
              ...prev,
              username: data.data.username,
              email: data.data.email || '',
              language: data.data.language || 'zh'
            }))
          }
        }
      } catch (error) {
        console.error('加载用户信息失败:', error)
      } finally {
        setIsLoading(false)
      }
    }

    loadUserProfile()
  }, [session, status])

  const handleProfileSave = async () => {
    if (!session) {
      alert('用户信息无效，请重新登录')
      return
    }

    setIsSavingProfile(true)

    try {
      // 验证基本信息
      if (!settings.username.trim()) {
        alert('请输入用户名')
        setIsSavingProfile(false)
        return
      }
      if (!settings.email.trim()) {
        alert('请输入邮箱地址')
        setIsSavingProfile(false)
        return
      }

      const response = await fetch('/api/user-profile', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          username: settings.username.trim(),
          email: settings.email.trim()
        })
      })

      const data = await response.json()

      if (data.success) {
        setShowProfileEdit(false)
        alert('个人信息保存成功！')
      } else {
        alert(data.error || '保存失败，请重试')
      }
    } catch (error) {
      console.error('保存个人信息失败:', error)
      alert('保存失败，请重试')
    } finally {
      setIsSavingProfile(false)
    }
  }

  const handlePasswordSave = async () => {
    if (!session) {
      alert('用户信息无效，请重新登录')
      return
    }

    setIsSavingPassword(true)

    try {
      // 验证密码字段
      if (!settings.currentPassword) {
        alert('请输入当前密码')
        setIsSavingPassword(false)
        return
      }
      if (!settings.newPassword) {
        alert('请输入新密码')
        setIsSavingPassword(false)
        return
      }
      if (settings.newPassword !== settings.confirmPassword) {
        alert('新密码和确认密码不匹配')
        setIsSavingPassword(false)
        return
      }
      if (settings.newPassword.length < 6) {
        alert('新密码长度至少6位')
        setIsSavingPassword(false)
        return
      }

      const response = await fetch('/api/change-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          currentPassword: settings.currentPassword,
          newPassword: settings.newPassword
        })
      })

      const data = await response.json()

      if (data.success) {
        // 清空密码字段
        setSettings(prev => ({
          ...prev,
          currentPassword: '',
          newPassword: '',
          confirmPassword: ''
        }))
        setShowPasswordFields(false)
        alert('密码修改成功！')
      } else {
        alert(data.error || '密码修改失败，请重试')
      }
    } catch (error) {
      console.error('修改密码失败:', error)
      alert('密码修改失败，请重试')
    } finally {
      setIsSavingPassword(false)
    }
  }

  const updateSettings = (key: keyof UserSettings, value: any) => {
    setSettings(prev => ({ ...prev, [key]: value }))
  }

  const handleLanguageChange = async (newLanguage: 'zh' | 'en') => {
    if (!session) {
      alert('用户信息无效，请重新登录')
      return
    }

    try {
      const response = await fetch('/api/user-language', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          language: newLanguage
        })
      })

      const data = await response.json()

      if (data.success) {
        updateSettings('language', newLanguage)
        alert('语言设置已保存！')
      } else {
        alert(data.error || '语言设置保存失败')
      }
    } catch (error) {
      console.error('保存语言设置失败:', error)
      alert('语言设置保存失败')
    }
  }



  if (status === 'loading' || isLoading) {
    return (
      <AuthLayout>
        <div className="min-h-screen bg-gray-50 py-4">
          <div className="max-w-4xl mx-auto px-4">
            <div className="bg-white border-2 border-black shadow-[2px_2px_0px_0px_rgba(0,0,0,1)]">
              <div className="bg-black text-white p-4">
                <h1 className="text-2xl font-bold font-mono mb-1">SETTINGS</h1>
                <p className="text-xs opacity-80">个人设置 / USER SETTINGS</p>
                <div className="w-12 h-0.5 bg-white mt-2"></div>
              </div>
              <div className="p-6 text-center">
                <div className="flex items-center justify-center space-x-2">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-black"></div>
                  <span className="font-mono text-sm">加载用户信息中...</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </AuthLayout>
    )
  }

  if (!session) {
    return null // AuthLayout会处理重定向
  }

  return (
    <AuthLayout>
      <div className="min-h-screen bg-gray-50 py-4">
        <div className="max-w-4xl mx-auto px-4">
          <div className="bg-white border-2 border-black shadow-[2px_2px_0px_0px_rgba(0,0,0,1)]">
            <div className="bg-black text-white p-4">
              <h1 className="text-2xl font-bold font-mono mb-1">SETTINGS</h1>
              <p className="text-xs opacity-80">个人设置 / USER SETTINGS</p>
              <div className="w-12 h-0.5 bg-white mt-2"></div>
            </div>
            
            <div className="p-6">
              <form className="space-y-6">
                {/* 个人信息部分 */}
                <div className="space-y-4">
                  <div className="border-b border-black pb-3">
                    <h2 className="text-xl font-bold text-black mb-1 font-mono">个人信息</h2>
                    <p className="text-gray-600 font-mono text-sm">PROFILE / USER INFO</p>
                  </div>

                  {/* 个人信息显示/编辑 */}
                  <div className="bg-gray-100 border border-black p-4">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-2">
                        <div className="w-6 h-6 bg-black text-white flex items-center justify-center text-xs">
                          👤
                        </div>
                        <div>
                          <h3 className="text-base font-bold text-black font-mono">个人信息</h3>
                          <p className="text-gray-600 text-xs">修改用户名和邮箱</p>
                        </div>
                      </div>
                      <button
                        type="button"
                        onClick={() => setShowProfileEdit(!showProfileEdit)}
                        className="px-3 py-1 bg-black text-white font-mono text-xs font-bold hover:bg-gray-800 transition-all border border-black"
                      >
                        {showProfileEdit ? '取消编辑' : '编辑信息'}
                      </button>
                    </div>

                    {!showProfileEdit ? (
                      // 显示模式
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-xs font-bold text-black mb-1 font-mono">用户名</label>
                          <div className="px-3 py-2 bg-white border border-gray-200 font-mono text-sm text-gray-700">
                            {settings.username}
                          </div>
                        </div>
                        <div>
                          <label className="block text-xs font-bold text-black mb-1 font-mono">邮箱地址</label>
                          <div className="px-3 py-2 bg-white border border-gray-200 font-mono text-sm text-gray-700">
                            {settings.email}
                          </div>
                        </div>
                      </div>
                    ) : (
                      // 编辑模式
                      <div className="mt-4 pt-4 border-t border-gray-300">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                          <div>
                            <label className="block text-xs font-bold text-black mb-1 font-mono">用户名</label>
                            <input
                              type="text"
                              value={settings.username}
                              onChange={(e) => updateSettings('username', e.target.value)}
                              className="w-full px-2 py-1 border border-gray-300 focus:border-black bg-white font-mono text-xs"
                              placeholder="请输入用户名"
                            />
                          </div>
                          <div>
                            <label className="block text-xs font-bold text-black mb-1 font-mono">邮箱地址</label>
                            <input
                              type="email"
                              value={settings.email}
                              onChange={(e) => updateSettings('email', e.target.value)}
                              className="w-full px-2 py-1 border border-gray-300 focus:border-black bg-white font-mono text-xs"
                              placeholder="请输入邮箱地址"
                            />
                          </div>
                        </div>

                        {/* 个人信息保存按钮 */}
                        <div className="flex justify-end">
                          <button
                            type="button"
                            onClick={handleProfileSave}
                            disabled={isSavingProfile}
                            className={`px-4 py-2 font-mono font-bold text-xs transition-all border shadow-[1px_1px_0px_0px_rgba(0,0,0,0.3)] ${
                              isSavingProfile
                                ? 'bg-gray-400 text-white cursor-not-allowed border-gray-400'
                                : 'bg-black text-white hover:bg-gray-800 border-black'
                            }`}
                          >
                            {isSavingProfile ? (
                              <div className="flex items-center space-x-2">
                                <div className="animate-spin rounded-full h-3 w-3 border-b border-white"></div>
                                <span>保存中...</span>
                              </div>
                            ) : (
                              <div className="flex items-center space-x-1">
                                <span>💾</span>
                                <span>保存信息</span>
                              </div>
                            )}
                          </button>
                        </div>
                      </div>
                    )}
                  </div>

                  {/* 密码修改 */}
                  <div className="bg-gray-100 border border-black p-4">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-2">
                        <div className="w-6 h-6 bg-black text-white flex items-center justify-center text-xs">
                          🔒
                        </div>
                        <div>
                          <h3 className="text-base font-bold text-black font-mono">密码设置</h3>
                          <p className="text-gray-600 text-xs">修改登录密码</p>
                        </div>
                      </div>
                      <button
                        type="button"
                        onClick={() => setShowPasswordFields(!showPasswordFields)}
                        className="px-3 py-1 bg-black text-white font-mono text-xs font-bold hover:bg-gray-800 transition-all border border-black"
                      >
                        {showPasswordFields ? '取消修改' : '修改密码'}
                      </button>
                    </div>

                    {showPasswordFields && (
                      <div className="mt-4 pt-4 border-t border-gray-300">
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-3 mb-4">
                          <div>
                            <label className="block text-xs font-bold text-black mb-1 font-mono">当前密码</label>
                            <input
                              type="password"
                              value={settings.currentPassword}
                              onChange={(e) => updateSettings('currentPassword', e.target.value)}
                              className="w-full px-2 py-1 border border-gray-300 focus:border-black bg-white font-mono text-xs"
                              placeholder="输入当前密码"
                            />
                          </div>
                          <div>
                            <label className="block text-xs font-bold text-black mb-1 font-mono">新密码</label>
                            <input
                              type="password"
                              value={settings.newPassword}
                              onChange={(e) => updateSettings('newPassword', e.target.value)}
                              className="w-full px-2 py-1 border border-gray-300 focus:border-black bg-white font-mono text-xs"
                              placeholder="输入新密码"
                            />
                          </div>
                          <div>
                            <label className="block text-xs font-bold text-black mb-1 font-mono">确认密码</label>
                            <input
                              type="password"
                              value={settings.confirmPassword}
                              onChange={(e) => updateSettings('confirmPassword', e.target.value)}
                              className="w-full px-2 py-1 border border-gray-300 focus:border-black bg-white font-mono text-xs"
                              placeholder="再次输入新密码"
                            />
                          </div>
                        </div>

                        {/* 密码保存按钮 */}
                        <div className="flex justify-end">
                          <button
                            type="button"
                            onClick={handlePasswordSave}
                            disabled={isSavingPassword}
                            className={`px-4 py-2 font-mono font-bold text-xs transition-all border shadow-[1px_1px_0px_0px_rgba(0,0,0,0.3)] ${
                              isSavingPassword
                                ? 'bg-gray-400 text-white cursor-not-allowed border-gray-400'
                                : 'bg-black text-white hover:bg-gray-800 border-black'
                            }`}
                          >
                            {isSavingPassword ? (
                              <div className="flex items-center space-x-2">
                                <div className="animate-spin rounded-full h-3 w-3 border-b border-white"></div>
                                <span>保存中...</span>
                              </div>
                            ) : (
                              <div className="flex items-center space-x-1">
                                <span>🔒</span>
                                <span>保存密码</span>
                              </div>
                            )}
                          </button>
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                {/* 界面设置部分 */}
                <div className="space-y-4">
                  <div className="border-b border-black pb-3">
                    <h2 className="text-xl font-bold text-black mb-1 font-mono">界面设置</h2>
                    <p className="text-gray-600 font-mono text-sm">INTERFACE / PREFERENCES</p>
                  </div>

                  <div className="bg-gray-100 border border-black p-4">
                    <div className="flex items-center space-x-2 mb-3">
                      <div className="w-6 h-6 bg-black text-white flex items-center justify-center text-xs">
                        🌐
                      </div>
                      <div>
                        <h3 className="text-base font-bold text-black font-mono">界面语言</h3>
                        <p className="text-gray-600 text-xs">选择界面显示语言（即时生效）</p>
                      </div>
                    </div>
                    <select
                      value={settings.language}
                      onChange={(e) => handleLanguageChange(e.target.value as 'zh' | 'en')}
                      className="w-full px-3 py-2 border border-gray-300 focus:border-black bg-white font-mono text-sm"
                    >
                      <option value="zh">🇨🇳 中文简体</option>
                      <option value="en">🇺🇸 English</option>
                    </select>
                  </div>
                </div>


              </form>
            </div>
          </div>
        </div>
      </div>
    </AuthLayout>
  )
}
