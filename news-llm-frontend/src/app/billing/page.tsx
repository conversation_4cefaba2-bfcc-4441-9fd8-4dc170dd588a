'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import AuthLayout from '@/components/AuthLayout'

interface BillingRecord {
  id: string
  orderId: string
  planName: string
  amount: number
  currency: string
  status: 'pending' | 'completed' | 'failed' | 'cancelled'
  paymentMethod: string
  createdAt: string
  paidAt?: string
  description: string
}

export default function BillingPage() {
  const { data: session } = useSession()
  const [billingRecords, setBillingRecords] = useState<BillingRecord[]>([])
  const [loading, setLoading] = useState(true)
  const [notification, setNotification] = useState<{
    message: string
    type: 'success' | 'error'
  } | null>(null)

  // 显示通知
  const showNotification = (message: string, type: 'success' | 'error') => {
    setNotification({ message, type })
    setTimeout(() => setNotification(null), 3000)
  }

  // 获取账单记录
  const fetchBillingRecords = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/billing')
      
      if (!response.ok) {
        throw new Error('Failed to fetch billing records')
      }

      const data = await response.json()
      setBillingRecords(data.records || [])
    } catch (error) {
      console.error('获取账单记录失败:', error)
      showNotification('获取账单记录失败', 'error')
      // 使用模拟数据作为备用
      setBillingRecords([
        {
          id: '1',
          orderId: 'ORDER_20250128_001',
          planName: 'Pro 月付',
          amount: 2900,
          currency: 'CNY',
          status: 'completed',
          paymentMethod: '微信支付',
          createdAt: '2025-01-28T10:30:00Z',
          paidAt: '2025-01-28T10:31:15Z',
          description: 'News-LLM Pro 月付订阅'
        },
        {
          id: '2',
          orderId: 'ORDER_20250101_002',
          planName: 'Pro 年付',
          amount: 29900,
          currency: 'CNY',
          status: 'completed',
          paymentMethod: '支付宝',
          createdAt: '2025-01-01T15:20:00Z',
          paidAt: '2025-01-01T15:21:30Z',
          description: 'News-LLM Pro 年付订阅'
        },
        {
          id: '3',
          orderId: 'ORDER_20241215_003',
          planName: 'Pro 月付',
          amount: 2900,
          currency: 'CNY',
          status: 'failed',
          paymentMethod: '微信支付',
          createdAt: '2024-12-15T09:15:00Z',
          description: 'News-LLM Pro 月付订阅'
        }
      ])
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (session?.user) {
      fetchBillingRecords()
    }
  }, [session])

  // 格式化金额
  const formatAmount = (amount: number, currency: string) => {
    const formattedAmount = (amount / 100).toFixed(2)
    return currency === 'CNY' ? `¥${formattedAmount}` : `$${formattedAmount}`
  }

  // 格式化日期
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  // 获取状态显示文本和样式
  const getStatusInfo = (status: string) => {
    switch (status) {
      case 'completed':
        return { text: '已完成', className: 'bg-green-100 text-green-800' }
      case 'pending':
        return { text: '待支付', className: 'bg-yellow-100 text-yellow-800' }
      case 'failed':
        return { text: '支付失败', className: 'bg-red-100 text-red-800' }
      case 'cancelled':
        return { text: '已取消', className: 'bg-gray-100 text-gray-800' }
      default:
        return { text: '未知', className: 'bg-gray-100 text-gray-800' }
    }
  }

  return (
    <AuthLayout>
      <div className="min-h-screen bg-gray-50">
        {/* 通知 */}
        {notification && (
          <div className={`fixed top-4 right-4 z-50 px-4 py-2 rounded-md text-white font-mono text-sm ${
            notification.type === 'success' ? 'bg-green-500' : 'bg-red-500'
          }`}>
            {notification.message}
          </div>
        )}

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* 页面标题 */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-black font-mono tracking-wider">BILLING</h1>
            <p className="text-gray-600 mt-2 font-mono">账单记录 / PAYMENT HISTORY</p>
          </div>

          {/* 账单记录 */}
          <div className="bg-white border border-black shadow-[3px_3px_0px_0px_rgba(0,0,0,1)]">
            <div className="bg-black text-white p-4 flex items-center justify-between">
              <div>
                <h2 className="text-lg font-bold tracking-wider font-mono">PAYMENT RECORDS</h2>
                <p className="text-xs opacity-80">支付记录 / TRANSACTION HISTORY</p>
              </div>
              <div className="text-xl font-bold">📄</div>
            </div>

            <div className="p-6">
              {loading ? (
                <div className="text-center py-8">
                  <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-black"></div>
                  <p className="mt-2 text-gray-600 font-mono">加载中...</p>
                </div>
              ) : billingRecords.length === 0 ? (
                <div className="text-center py-8">
                  <div className="text-6xl mb-4">📄</div>
                  <h3 className="text-lg font-bold text-gray-900 font-mono mb-2">暂无账单记录</h3>
                  <p className="text-gray-600 font-mono">您还没有任何付费记录</p>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="w-full border-collapse">
                    <thead>
                      <tr className="border-b-2 border-black">
                        <th className="text-left py-3 px-4 font-mono font-bold">订单号</th>
                        <th className="text-left py-3 px-4 font-mono font-bold">套餐</th>
                        <th className="text-left py-3 px-4 font-mono font-bold">金额</th>
                        <th className="text-left py-3 px-4 font-mono font-bold">支付方式</th>
                        <th className="text-left py-3 px-4 font-mono font-bold">状态</th>
                        <th className="text-left py-3 px-4 font-mono font-bold">创建时间</th>
                        <th className="text-left py-3 px-4 font-mono font-bold">支付时间</th>
                      </tr>
                    </thead>
                    <tbody>
                      {billingRecords.map((record) => {
                        const statusInfo = getStatusInfo(record.status)
                        return (
                          <tr key={record.id} className="border-b border-gray-200 hover:bg-gray-50">
                            <td className="py-3 px-4 font-mono text-sm">{record.orderId}</td>
                            <td className="py-3 px-4 font-mono text-sm font-bold">{record.planName}</td>
                            <td className="py-3 px-4 font-mono text-sm font-bold">
                              {formatAmount(record.amount, record.currency)}
                            </td>
                            <td className="py-3 px-4 font-mono text-sm">{record.paymentMethod}</td>
                            <td className="py-3 px-4">
                              <span className={`px-2 py-1 rounded-full text-xs font-mono ${statusInfo.className}`}>
                                {statusInfo.text}
                              </span>
                            </td>
                            <td className="py-3 px-4 font-mono text-sm text-gray-600">
                              {formatDate(record.createdAt)}
                            </td>
                            <td className="py-3 px-4 font-mono text-sm text-gray-600">
                              {record.paidAt ? formatDate(record.paidAt) : '-'}
                            </td>
                          </tr>
                        )
                      })}
                    </tbody>
                  </table>
                </div>
              )}
            </div>
          </div>


        </div>
      </div>
    </AuthLayout>
  )
}
