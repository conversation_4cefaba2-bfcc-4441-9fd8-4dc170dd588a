'use client'

import { useState, useEffect } from 'react'
import AuthLayout from '@/components/AuthLayout'
import AddSubscriptionModal from '@/components/AddSubscriptionModal'

interface Subscription {
  id: string
  name: string
  url: string
  category: string
  isActive: boolean
  lastUpdated: string
  articleCount: number
  description: string
}

export default function MySubscriptionsPage() {
  const [subscriptions, setSubscriptions] = useState<Subscription[]>([])
  const [loading, setLoading] = useState(true)
  const [isAddSubscriptionModalOpen, setIsAddSubscriptionModalOpen] = useState(false)
  const [selectedCategory, setSelectedCategory] = useState('全部')
  const [searchTerm, setSearchTerm] = useState('')

  // 获取用户订阅数据
  const fetchUserSubscriptions = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/subscription')
      if (!response.ok) {
        throw new Error('Failed to fetch subscriptions')
      }
      const data = await response.json()

      if (data.success && data.feeds) {
        const convertedSources = data.feeds.map((feed: any) => ({
          id: feed.id.toString(),
          name: feed.title,
          url: feed.feed_url,
          description: feed.site_url || '暂无描述',
          category: feed.category?.title || '默认分类',
          isActive: !feed.disabled,
          lastUpdated: feed.checked_at || new Date().toISOString(),
          articleCount: feed.unread_count || 0
        }))
        setSubscriptions(convertedSources)
      }
    } catch (error) {
      console.error('获取订阅失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 页面加载时获取订阅数据
  useEffect(() => {
    fetchUserSubscriptions()
  }, [])

  const toggleSubscription = async (id: string) => {
    try {
      const currentSub = subscriptions.find(sub => sub.id === id)
      if (!currentSub) return

      const response = await fetch(`/api/subscription/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ disabled: currentSub.isActive })
      })

      if (response.ok) {
        setSubscriptions(prev => prev.map(sub =>
          sub.id === id ? { ...sub, isActive: !sub.isActive } : sub
        ))
      }
    } catch (error) {
      console.error('切换订阅状态失败:', error)
    }
  }

  const removeSubscription = async (id: string) => {
    try {
      const response = await fetch(`/api/subscription/${id}`, {
        method: 'DELETE'
      })

      if (response.ok) {
        setSubscriptions(prev => prev.filter(sub => sub.id !== id))
      }
    } catch (error) {
      console.error('删除订阅失败:', error)
    }
  }

  const addSubscription = (newSub: Omit<Subscription, 'id' | 'lastUpdated' | 'articleCount'>) => {
    const subscription: Subscription = {
      ...newSub,
      id: Date.now().toString(),
      lastUpdated: new Date().toISOString(),
      articleCount: 0
    }
    setSubscriptions(prev => [...prev, subscription])
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))
    
    if (diffInHours < 1) return '刚刚'
    if (diffInHours < 24) return `${diffInHours}小时前`
    return `${Math.floor(diffInHours / 24)}天前`
  }

  // 获取所有分类
  const categories = ['全部', ...Array.from(new Set(subscriptions.map(sub => sub.category)))]

  // 过滤订阅
  const filteredSubscriptions = subscriptions.filter(sub => {
    const matchesCategory = selectedCategory === '全部' || sub.category === selectedCategory
    const matchesSearch = searchTerm === '' || 
      sub.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      sub.description.toLowerCase().includes(searchTerm.toLowerCase())
    return matchesCategory && matchesSearch
  })

  // 统计数据
  const stats = {
    total: subscriptions.length,
    active: subscriptions.filter(sub => sub.isActive).length,
    paused: subscriptions.filter(sub => !sub.isActive).length,
    totalArticles: subscriptions.reduce((sum, sub) => sum + sub.articleCount, 0)
  }

  return (
    <AuthLayout>
      <div className="min-h-screen bg-gray-50 py-4">
        <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* 页面标题 - 紧凑版 */}
          <div className="bg-white border border-black shadow-[2px_2px_0px_0px_rgba(0,0,0,1)] mb-4">
            <div className="bg-black text-white p-4 flex items-center justify-between">
              <div>
                <h1 className="text-xl font-bold tracking-wider font-mono">我的订阅</h1>
                <p className="text-xs opacity-80">共 {stats.total} 个订阅源，{stats.active} 个活跃</p>
              </div>
              <button
                onClick={() => setIsAddSubscriptionModalOpen(true)}
                className="bg-white text-black px-4 py-2 font-mono font-bold text-xs hover:bg-gray-100 transition-all"
              >
                + 添加
              </button>
            </div>
          </div>

          {/* 搜索和过滤 - 紧凑版 */}
          <div className="bg-white border border-black shadow-[1px_1px_0px_0px_rgba(0,0,0,1)] p-3 mb-4">
            <div className="flex flex-col sm:flex-row gap-3">
              <div className="flex-1">
                <input
                  type="text"
                  placeholder="搜索订阅源..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 focus:border-black font-mono text-xs"
                />
              </div>
              <div className="flex flex-wrap gap-1">
                {categories.slice(0, 6).map(category => (
                  <button
                    key={category}
                    onClick={() => setSelectedCategory(category)}
                    className={`px-3 py-2 font-mono text-xs font-bold border transition-all ${
                      selectedCategory === category
                        ? 'bg-black text-white border-black'
                        : 'bg-white text-black border-gray-300 hover:border-black'
                    }`}
                  >
                    {category}
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* 订阅列表 - 紧凑版 */}
          {loading ? (
            <div className="text-center py-8">
              <div className="text-4xl mb-2">⏳</div>
              <p className="font-mono text-sm text-gray-600">加载中...</p>
            </div>
          ) : filteredSubscriptions.length === 0 ? (
            <div className="bg-white border border-black shadow-[1px_1px_0px_0px_rgba(0,0,0,1)] p-8 text-center">
              <div className="text-4xl mb-4">📭</div>
              <h3 className="text-lg font-bold text-black font-mono mb-2">
                {searchTerm || selectedCategory !== '全部' ? '没有找到匹配的订阅源' : '还没有订阅源'}
              </h3>
              <p className="text-gray-600 font-mono text-xs mb-4">
                {searchTerm || selectedCategory !== '全部'
                  ? '尝试调整搜索条件或分类筛选'
                  : '开始添加您感兴趣的RSS订阅源吧'
                }
              </p>
              {!searchTerm && selectedCategory === '全部' && (
                <button
                  onClick={() => setIsAddSubscriptionModalOpen(true)}
                  className="bg-black text-white px-6 py-2 font-mono font-bold text-sm hover:bg-gray-800 transition-all border border-black"
                >
                  添加第一个订阅源
                </button>
              )}
            </div>
          ) : (
            <div className="space-y-3">
              {filteredSubscriptions.map((subscription) => (
                <div key={subscription.id} className="bg-white border border-black shadow-[1px_1px_0px_0px_rgba(0,0,0,1)] hover:shadow-[2px_2px_0px_0px_rgba(0,0,0,1)] transition-all">
                  <div className="p-4">
                    <div className="flex items-center justify-between mb-3">
                      {/* 左侧：订阅源信息 */}
                      <div className="flex items-center space-x-3 flex-1">
                        <div className="w-8 h-8 bg-black text-white flex items-center justify-center font-bold text-sm">
                          {subscription.name.charAt(0).toUpperCase()}
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center space-x-2 mb-1">
                            <h3 className="font-bold text-sm truncate">{subscription.name}</h3>
                            <span className={`px-2 py-1 text-xs font-bold border ${
                              subscription.isActive
                                ? 'bg-green-100 text-green-800 border-green-300'
                                : 'bg-gray-100 text-gray-600 border-gray-300'
                            }`}>
                              {subscription.isActive ? '活跃' : '暂停'}
                            </span>
                          </div>
                          <div className="flex items-center space-x-4 text-xs text-gray-600">
                            <span className="bg-gray-100 px-2 py-1 font-mono">{subscription.category}</span>
                            <span>{subscription.articleCount} 篇未读</span>
                            <span>{formatDate(subscription.lastUpdated)}</span>
                          </div>
                        </div>
                      </div>

                      {/* 右侧：操作按钮 */}
                      <div className="flex items-center space-x-2 ml-4">
                        <button
                          onClick={() => toggleSubscription(subscription.id)}
                          className={`px-3 py-1 font-mono text-xs font-bold border transition-all ${
                            subscription.isActive
                              ? 'bg-gray-200 text-black border-gray-400 hover:bg-gray-300'
                              : 'bg-black text-white border-black hover:bg-gray-800'
                          }`}
                        >
                          {subscription.isActive ? '暂停' : '恢复'}
                        </button>
                        <button
                          onClick={() => removeSubscription(subscription.id)}
                          className="px-3 py-1 bg-white text-red-600 border border-red-300 hover:border-red-500 hover:bg-red-50 font-mono text-xs font-bold transition-all"
                        >
                          删除
                        </button>
                      </div>
                    </div>

                    {/* 描述和URL */}
                    <div className="text-xs text-gray-600 pl-11">
                      <p className="mb-1 truncate" title={subscription.description}>{subscription.description}</p>
                      <p className="font-mono text-gray-500 truncate" title={subscription.url}>{subscription.url}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* 添加订阅模态框 */}
      <AddSubscriptionModal
        isOpen={isAddSubscriptionModalOpen}
        onClose={() => setIsAddSubscriptionModalOpen(false)}
        onAdd={addSubscription}
      />
    </AuthLayout>
  )
}
