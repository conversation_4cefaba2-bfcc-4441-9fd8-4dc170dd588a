'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import Navigation from '@/components/Navigation'

export default function TrialPage() {
  const { data: session } = useSession()
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)
  const [trialStatus, setTrialStatus] = useState<'available' | 'claimed' | 'active' | 'expired'>('available')
  const [trialEndDate, setTrialEndDate] = useState<string | null>(null)
  const [currentTime, setCurrentTime] = useState('')
  const [urgencyCounter, setUrgencyCounter] = useState(0)

  // 实时时间和紧迫感计数器
  useEffect(() => {
    const updateTime = () => {
      const now = new Date()
      setCurrentTime(now.toLocaleTimeString('zh-CN'))
      // 模拟剩余试用名额递减
      setUrgencyCounter(Math.floor((now.getHours() * 60 + now.getMinutes()) * 0.1))
    }

    updateTime()
    const timer = setInterval(updateTime, 1000)
    return () => clearInterval(timer)
  }, [])

  // 检查试用状态
  useEffect(() => {
    const checkTrialStatus = async () => {
      if (!session?.user?.id) return

      try {
        const response = await fetch(`/api/subscription?user_id=${session.user.id}`)
        if (response.ok) {
          const data = await response.json()
          if (data.trial_availed) {
            if (data.status === 'trialing') {
              setTrialStatus('active')
              setTrialEndDate(data.trial_end_at)
            } else {
              setTrialStatus('expired')
            }
          } else {
            setTrialStatus('available')
          }
        }
      } catch (error) {
        // 静默处理错误
      }
    }

    checkTrialStatus()
  }, [session])

  const handleClaimTrial = async () => {
    if (!session?.user?.id) {
      // 未登录用户引导到注册页面，并带上试用参数
      router.push('/auth/signup?trial=true')
      return
    }

    setIsLoading(true)

    try {
      const response = await fetch('/api/trial/claim', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          user_id: session.user.id
        })
      })

      if (response.ok) {
        const data = await response.json()
        setTrialStatus('active')
        setTrialEndDate(data.trial_end_at)
      } else {
        const error = await response.json()
        alert(error.message || '领取试用失败')
      }
    } catch (error) {
      alert('领取试用失败，请稍后重试')
    } finally {
      setIsLoading(false)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-red-50 via-orange-50 to-yellow-50">
      <Navigation />

      {/* 紧迫感横幅 */}
      <div className="bg-red-600 text-white py-2 px-4 text-center font-mono text-sm animate-pulse">
        ⚠️ 限时试用 {currentTime} - 仅剩 {247 - urgencyCounter} 个免费名额 ⚠️
      </div>

      <div className="py-16 px-4">
        <div className="max-w-6xl mx-auto">
          {/* 主标题 - 恐惧 + 渴望驱动 */}
          <div className="text-center mb-16">
            <div className="bg-gradient-to-r from-purple-600 to-pink-600 text-white p-8 border-4 border-black shadow-[8px_8px_0px_0px_rgba(0,0,0,1)] mb-8">
              <h1 className="text-4xl md:text-6xl font-bold font-mono mb-4 text-yellow-300">
                🎁 7天免费试用
              </h1>
              <p className="text-xl md:text-2xl mb-4">
                体验价值 ¥133 的专业版功能
              </p>
              <p className="text-lg text-pink-200">
                错过这次机会，可能要等很久...
              </p>
            </div>
          </div>

          {/* 社会证明 + 稀缺性 */}
          <div className="bg-black text-white p-8 mb-16 border-4 border-yellow-400 shadow-[8px_8px_0px_0px_rgba(250,204,21,1)]">
            <div className="text-center mb-8">
              <h2 className="text-3xl font-bold font-mono mb-4 text-yellow-400">
                🔥 内测用户疯狂好评
              </h2>
              <div className="grid md:grid-cols-3 gap-4 text-center">
                <div>
                  <div className="text-3xl font-bold text-yellow-400">4,521</div>
                  <p className="text-sm">已领取试用</p>
                </div>
                <div>
                  <div className="text-3xl font-bold text-yellow-400">96.8%</div>
                  <p className="text-sm">转化为付费用户</p>
                </div>
                <div>
                  <div className="text-3xl font-bold text-yellow-400">4.9★</div>
                  <p className="text-sm">用户满意度</p>
                </div>
              </div>
            </div>

            {/* 用户证言 */}
            <div className="grid md:grid-cols-2 gap-6">
              <div className="bg-white/10 p-4 rounded border border-white/20">
                <div className="text-yellow-400 mb-2">⭐⭐⭐⭐⭐</div>
                <p className="mb-2">"试用第一天就被震撼了！AI筛选太准了，再也不用被垃圾信息轰炸"</p>
                <p className="text-xs text-gray-300">- 王总，投资人</p>
              </div>
              <div className="bg-white/10 p-4 rounded border border-white/20">
                <div className="text-yellow-400 mb-2">⭐⭐⭐⭐⭐</div>
                <p className="mb-2">"7天试用结束后立即付费，这是我用过最好的新闻平台"</p>
                <p className="text-xs text-gray-300">- 李经理，科技公司</p>
              </div>
            </div>
          </div>

          {/* 价值展示 - 损失厌恶 */}
          <div className="bg-white p-8 border-4 border-green-500 shadow-[8px_8px_0px_0px_rgba(34,197,94,1)] mb-16">
            <div className="text-center mb-8">
              <h2 className="text-3xl font-bold font-mono mb-4 text-green-600">
                💰 7天试用价值计算
              </h2>
            </div>

            <div className="grid md:grid-cols-2 gap-8">
              {/* 左侧：免费用户的痛苦 */}
              <div className="bg-red-50 border-2 border-red-500 p-6 rounded-lg">
                <h3 className="text-xl font-bold text-red-600 mb-4 text-center">😫 免费版限制</h3>
                <div className="space-y-3">
                  <div className="flex items-center">
                    <span className="text-red-500 mr-3">❌</span>
                    <span>只能看3.9★以下内容</span>
                  </div>
                  <div className="flex items-center">
                    <span className="text-red-500 mr-3">❌</span>
                    <span>高分内容被锁定</span>
                  </div>
                  <div className="flex items-center">
                    <span className="text-red-500 mr-3">❌</span>
                    <span>仅限10个订阅源</span>
                  </div>
                  <div className="flex items-center">
                    <span className="text-red-500 mr-3">❌</span>
                    <span>错过重要信息</span>
                  </div>
                </div>
                <div className="mt-4 p-3 bg-red-100 border border-red-300 rounded text-center">
                  <p className="text-red-700 font-bold">
                    💸 信息损失价值：¥133/周
                  </p>
                </div>
              </div>

              {/* 右侧：专业版的快乐 */}
              <div className="bg-green-50 border-2 border-green-500 p-6 rounded-lg">
                <h3 className="text-xl font-bold text-green-600 mb-4 text-center">🚀 专业版特权</h3>
                <div className="space-y-3">
                  <div className="flex items-center">
                    <span className="text-green-500 mr-3">✅</span>
                    <span>5.0★顶级内容解锁</span>
                  </div>
                  <div className="flex items-center">
                    <span className="text-green-500 mr-3">✅</span>
                    <span>AI智能摘要，3秒看懂</span>
                  </div>
                  <div className="flex items-center">
                    <span className="text-green-500 mr-3">✅</span>
                    <span>100个订阅源配额</span>
                  </div>
                  <div className="flex items-center">
                    <span className="text-green-500 mr-3">✅</span>
                    <span>纯净无广告体验</span>
                  </div>
                </div>
                <div className="mt-4 p-3 bg-green-100 border border-green-300 rounded text-center">
                  <p className="text-green-700 font-bold">
                    💎 免费体验价值：¥133
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* 主要行动召唤 */}
          {trialStatus === 'available' && (
            <div className="bg-gradient-to-r from-yellow-400 to-orange-500 text-black p-8 border-4 border-red-500 shadow-[8px_8px_0px_0px_rgba(239,68,68,1)] mb-16">
              <div className="text-center">
                <h2 className="text-3xl md:text-4xl font-bold font-mono mb-4">
                  ⚡ 立即领取7天免费试用
                </h2>
                <p className="text-xl mb-6">
                  价值 ¥133 的专业版功能，完全免费体验
                </p>

                <div className="mb-8">
                  <div className="bg-black/20 p-4 rounded-lg border border-black/30 mb-4">
                    <div className="grid md:grid-cols-3 gap-4 text-center">
                      <div>
                        <div className="text-2xl font-bold text-red-700">⏰ 限时</div>
                        <p className="text-sm">仅剩 {247 - urgencyCounter} 个名额</p>
                      </div>
                      <div>
                        <div className="text-2xl font-bold text-red-700">🎯 免费</div>
                        <p className="text-sm">无需信用卡</p>
                      </div>
                      <div>
                        <div className="text-2xl font-bold text-red-700">🛡️ 安全</div>
                        <p className="text-sm">随时可取消</p>
                      </div>
                    </div>
                  </div>

                  <button
                    onClick={handleClaimTrial}
                    disabled={isLoading}
                    className={`w-full md:w-auto px-12 py-6 bg-black text-white font-mono font-bold text-2xl border-4 border-white shadow-[8px_8px_0px_0px_rgba(255,255,255,1)] hover:shadow-[12px_12px_0px_0px_rgba(255,255,255,1)] transition-all duration-300 transform hover:-translate-y-2 ${
                      isLoading ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-800'
                    }`}
                  >
                    {isLoading ? (
                      <div className="flex items-center justify-center">
                        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-white mr-3"></div>
                        {session ? '正在开通试用...' : '跳转注册页面...'}
                      </div>
                    ) : (
                      <div className="flex items-center justify-center">
                        🎁 {session ? '立即领取试用' : '注册并领取试用'}
                        <span className="ml-2">→</span>
                      </div>
                    )}
                  </button>

                  {!session && (
                    <p className="text-sm mt-4 text-black/70">
                      未登录用户将跳转到注册页面，注册后自动开通试用
                    </p>
                  )}
                </div>

                {/* 风险消除 */}
                <div className="bg-white/20 p-4 rounded-lg border border-white/30">
                  <h3 className="font-bold mb-3">🛡️ 零风险承诺</h3>
                  <div className="grid md:grid-cols-3 gap-4 text-sm">
                    <div>
                      <div className="text-lg mb-1">💰</div>
                      <p>试用期内随时取消</p>
                    </div>
                    <div>
                      <div className="text-lg mb-1">🔒</div>
                      <p>数据安全保护</p>
                    </div>
                    <div>
                      <div className="text-lg mb-1">📞</div>
                      <p>24/7客服支持</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* 试用已激活状态 */}
          {trialStatus === 'active' && trialEndDate && (
            <div className="bg-gradient-to-r from-green-400 to-blue-500 text-white p-8 border-4 border-black shadow-[8px_8px_0px_0px_rgba(0,0,0,1)] mb-16">
              <div className="text-center">
                <div className="text-6xl mb-6">🎉</div>
                <h2 className="text-4xl font-bold font-mono mb-4">
                  试用已激活！
                </h2>
                <p className="text-xl mb-6">
                  恭喜！您的7天专业版试用已成功开通
                </p>

                <div className="bg-white/20 p-6 rounded-lg border border-white/30 mb-8">
                  <div className="flex items-center justify-center mb-4">
                    <span className="text-2xl mr-3">⏰</span>
                    <span className="text-xl font-bold">试用到期时间</span>
                  </div>
                  <p className="text-2xl font-mono bg-white text-black px-6 py-3 rounded border-2 border-black">
                    {formatDate(trialEndDate)}
                  </p>
                </div>

                <div className="space-y-4">
                  <button
                    onClick={() => router.push('/news')}
                    className="bg-white text-black px-12 py-4 font-mono font-bold text-xl border-4 border-black shadow-[8px_8px_0px_0px_rgba(0,0,0,1)] hover:shadow-[12px_12px_0px_0px_rgba(0,0,0,1)] transition-all duration-300 transform hover:-translate-y-2"
                  >
                    🚀 立即开始体验专业版
                  </button>

                  <p className="text-lg">
                    现在就去体验5.0★顶级内容和AI智能摘要吧！
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* 试用已领取状态 */}
          {trialStatus === 'claimed' && (
            <div className="bg-orange-100 border-4 border-orange-500 p-8 shadow-[8px_8px_0px_0px_rgba(249,115,22,1)] mb-16">
              <div className="text-center">
                <div className="text-6xl mb-6">⏰</div>
                <h2 className="text-3xl font-bold font-mono mb-4 text-orange-800">
                  试用已领取
                </h2>
                <p className="text-xl text-orange-700 mb-8">
                  您已经领取过试用，每个账号仅限一次机会
                </p>

                <div className="bg-white p-6 border-2 border-orange-300 rounded-lg mb-8">
                  <h3 className="text-xl font-bold mb-4 text-orange-800">
                    💎 继续享受专业版特权
                  </h3>
                  <p className="text-orange-700 mb-4">
                    体验过专业版的强大功能，现在升级享受完整服务
                  </p>
                </div>

                <button
                  onClick={() => router.push('/pricing')}
                  className="bg-orange-600 text-white px-10 py-4 font-mono font-bold text-xl border-4 border-black shadow-[8px_8px_0px_0px_rgba(0,0,0,1)] hover:shadow-[12px_12px_0px_0px_rgba(0,0,0,1)] transition-all duration-300 transform hover:-translate-y-2"
                >
                  💰 查看付费方案 →
                </button>
              </div>
            </div>
          )}

          {/* 试用已过期状态 */}
          {trialStatus === 'expired' && (
            <div className="bg-red-100 border-4 border-red-500 p-8 shadow-[8px_8px_0px_0px_rgba(239,68,68,1)] mb-16">
              <div className="text-center">
                <div className="text-6xl mb-6">⌛</div>
                <h2 className="text-3xl font-bold font-mono mb-4 text-red-800">
                  试用已过期
                </h2>
                <p className="text-xl text-red-700 mb-8">
                  您的7天试用期已结束，升级到专业版继续享受完整功能
                </p>

                <div className="bg-white p-6 border-2 border-red-300 rounded-lg mb-8">
                  <h3 className="text-xl font-bold mb-4 text-red-800">
                    😢 回到免费版的限制
                  </h3>
                  <div className="text-left space-y-2 text-red-700">
                    <div>❌ 高分内容重新锁定</div>
                    <div>❌ 无法使用AI智能摘要</div>
                    <div>❌ 订阅源从100个降至10个</div>
                  </div>
                </div>

                <button
                  onClick={() => router.push('/pricing')}
                  className="bg-red-600 text-white px-10 py-4 font-mono font-bold text-xl border-4 border-black shadow-[8px_8px_0px_0px_rgba(0,0,0,1)] hover:shadow-[12px_12px_0px_0px_rgba(0,0,0,1)] transition-all duration-300 transform hover:-translate-y-2"
                >
                  🚀 立即升级专业版 →
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
