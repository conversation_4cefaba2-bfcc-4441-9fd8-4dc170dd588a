generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}



model entries {
  id               BigInt                   @id @default(autoincrement())
  user_id          Int
  feed_id          BigInt
  hash             String
  published_at     DateTime                 @db.Timestamptz(6)
  title            String
  url              String
  author           String?
  content          String?
  status           entry_status?            @default(unread)
  starred          Boolean?                 @default(false)
  comments_url     String?                  @default("")
  document_vectors Unsupported("tsvector")?
  changed_at       DateTime                 @db.Timestamptz(6)
  share_code       String                   @default("")
  reading_time     Int                      @default(0)
  created_at       DateTime                 @default(now()) @db.Timestamptz(6)
  tags             String[]                 @default([])
  ai_summary       String?
  ai_score         Decimal?                 @default(0.00) @db.Decimal(3, 2)
  sentiment        String?                  @default("neutral") @db.VarChar(20)
  quality_score    Decimal?                 @default(0.00) @db.Decimal(3, 2)
  ai_processed     Boolean?                 @default(false)
  ai_processed_at  DateTime?                @db.Timestamp(6)
  category         String?                  @db.VarChar(100)
  enclosures       enclosures[]
  feed             feeds                    @relation(fields: [feed_id], references: [id], onDelete: Cascade, onUpdate: NoAction)
  user             users                    @relation(fields: [user_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@unique([feed_id, hash])
  @@index([document_vectors], map: "document_vectors_idx", type: Gin)
  @@index([feed_id, status, hash])
  @@index([feed_id], map: "entries_feed_idx")
  @@index([id, user_id, status], map: "entries_id_user_status_idx")
  @@index([user_id, feed_id], map: "entries_user_feed_idx")
  @@index([user_id, status, starred])
  @@index([user_id, status, changed_at], map: "entries_user_status_changed_idx")
  @@index([user_id, status, changed_at, published_at], map: "entries_user_status_changed_published_idx")
  @@index([user_id, status, created_at], map: "entries_user_status_created_idx")
  @@index([user_id, status, feed_id], map: "entries_user_status_feed_idx")
  @@index([user_id, status], map: "entries_user_status_idx")
  @@index([user_id, status, published_at], map: "entries_user_status_published_idx")
  @@index([ai_processed], map: "idx_entries_ai_processed")
  @@index([ai_score(sort: Desc)], map: "idx_entries_ai_score")
  @@index([category], map: "idx_entries_category")
  @@index([sentiment], map: "idx_entries_sentiment")
  @@map("entries")
}

model feeds {
  id                             BigInt                    @id @default(autoincrement())
  user_id                        Int
  category_id                    Int
  title                          String
  feed_url                       String
  site_url                       String
  checked_at                     DateTime?                 @default(now()) @db.Timestamptz(6)
  etag_header                    String?                   @default("")
  last_modified_header           String?                   @default("")
  parsing_error_msg              String?                   @default("")
  parsing_error_count            Int?                      @default(0)
  scraper_rules                  String?                   @default("")
  rewrite_rules                  String?                   @default("")
  crawler                        Boolean?                  @default(false)
  username                       String?                   @default("")
  password                       String?                   @default("")
  user_agent                     String?                   @default("")
  disabled                       Boolean?                  @default(false)
  next_check_at                  DateTime?                 @default(now()) @db.Timestamptz(6)
  ignore_http_cache              Boolean?                  @default(false)
  fetch_via_proxy                Boolean?                  @default(false)
  blocklist_rules                String                    @default("")
  keeplist_rules                 String                    @default("")
  allow_self_signed_certificates Boolean                   @default(false)
  cookie                         String?                   @default("")
  hide_globally                  Boolean                   @default(false)
  url_rewrite_rules              String                    @default("")
  no_media_player                Boolean?                  @default(false)
  apprise_service_urls           String?                   @default("")
  disable_http2                  Boolean?                  @default(false)
  description                    String?                   @default("")
  ntfy_enabled                   Boolean?                  @default(false)
  ntfy_priority                  Int?                      @default(3)
  webhook_url                    String?                   @default("")
  pushover_enabled               Boolean?                  @default(false)
  pushover_priority              Int?                      @default(0)
  ntfy_topic                     String?                   @default("")
  proxy_url                      String?                   @default("")
  block_filter_entry_rules       String                    @default("")
  keep_filter_entry_rules        String                    @default("")
  entries                        entries[]
  feed_icons                     feed_icons[]
  category                       categories                @relation(fields: [category_id], references: [id], onDelete: Cascade, onUpdate: NoAction)
  user                           users                     @relation(fields: [user_id], references: [id], onDelete: Cascade, onUpdate: NoAction)
  user_feed_subscriptions        user_feed_subscriptions[]

  @@unique([user_id, feed_url])
  @@index([id, hide_globally], map: "feeds_feed_id_hide_globally_idx")
  @@index([user_id, category_id], map: "feeds_user_category_idx")
  @@map("feeds")
}

model users {
  id                                   Int                       @id @default(autoincrement())
  username                             String                    @unique
  password                             String?
  is_admin                             Boolean?                  @default(false)
  language                             String?                   @default("en_US")
  timezone                             String?                   @default("UTC")
  theme                                String?                   @default("light_serif")
  last_login_at                        DateTime?                 @db.Timestamptz(6)
  entry_direction                      entry_sorting_direction?  @default(asc)
  keyboard_shortcuts                   Boolean?                  @default(true)
  entries_per_page                     Int?                      @default(100)
  show_reading_time                    Boolean?                  @default(true)
  entry_swipe                          Boolean?                  @default(true)
  stylesheet                           String                    @default("")
  google_id                            String                    @default("")
  openid_connect_id                    String                    @default("")
  display_mode                         webapp_display_mode?      @default(standalone)
  entry_order                          entry_sorting_order?      @default(published_at)
  default_reading_speed                Int?                      @default(265)
  cjk_reading_speed                    Int?                      @default(500)
  default_home_page                    String?                   @default("unread")
  categories_sorting_order             String                    @default("unread_count")
  gesture_nav                          String?                   @default("tap")
  mark_read_on_view                    Boolean?                  @default(true)
  media_playback_rate                  Decimal?                  @default(1) @db.Decimal
  block_filter_entry_rules             String                    @default("")
  keep_filter_entry_rules              String                    @default("")
  mark_read_on_media_player_completion Boolean?                  @default(false)
  custom_js                            String                    @default("")
  external_font_hosts                  String                    @default("")
  always_open_external_links           Boolean?                  @default(false)
  open_external_links_in_new_tab       Boolean?                  @default(true)
  email                                String?                   @unique
  email_verified                       Boolean?                  @default(false)
  created_at                           DateTime?                 @default(now()) @db.Timestamptz(6)
  updated_at                           DateTime?                 @default(now()) @db.Timestamptz(6)
  emailVerified                        DateTime?                 @db.Timestamptz(6)
  api_keys                             api_keys[]
  categories                           categories[]
  enclosures                           enclosures[]
  entries                              entries[]
  feeds                                feeds[]
  user_feed_subscriptions              user_feed_subscriptions[]
  user_sessions                        user_sessions[]
  user_subscriptions                   user_subscriptions[]
  orders                               orders[]
  webauthn_credentials                 webauthn_credentials[]

  @@map("users")
}

model api_keys {
  id           Int       @id @default(autoincrement())
  user_id      Int
  token        String    @unique
  description  String
  last_used_at DateTime? @db.Timestamptz(6)
  created_at   DateTime? @default(now()) @db.Timestamptz(6)
  user         users     @relation(fields: [user_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@unique([user_id, description])
  @@map("api_keys")
}

model categories {
  id            Int     @id @default(autoincrement())
  user_id       Int
  title         String
  hide_globally Boolean @default(false)
  user          users   @relation(fields: [user_id], references: [id], onDelete: Cascade, onUpdate: NoAction)
  feeds         feeds[]

  @@unique([user_id, title])
  @@map("categories")
}

/// This model contains an expression index which requires additional setup for migrations. Visit https://pris.ly/d/expression-indexes for more info.
model enclosures {
  id                BigInt  @id @default(autoincrement())
  user_id           Int
  entry_id          BigInt
  url               String
  size              BigInt? @default(0)
  mime_type         String? @default("")
  media_progression Int?    @default(0)
  entries           entries @relation(fields: [entry_id], references: [id], onDelete: Cascade, onUpdate: NoAction)
  users             users   @relation(fields: [user_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@index([entry_id])
}

model feed_icons {
  feed_id BigInt
  icon_id BigInt
  feeds   feeds  @relation(fields: [feed_id], references: [id], onDelete: Cascade, onUpdate: NoAction)
  icons   icons  @relation(fields: [icon_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@id([feed_id, icon_id])
}

model icons {
  id          BigInt       @id @default(autoincrement())
  hash        String       @unique
  mime_type   String
  content     Bytes
  external_id String?      @default("")
  feed_icons  feed_icons[]
}

model integrations {
  user_id                               Int      @id
  pinboard_enabled                      Boolean? @default(false)
  pinboard_token                        String?  @default("")
  pinboard_tags                         String?  @default("miniflux")
  pinboard_mark_as_unread               Boolean? @default(false)
  instapaper_enabled                    Boolean? @default(false)
  instapaper_username                   String?  @default("")
  instapaper_password                   String?  @default("")
  fever_enabled                         Boolean? @default(false)
  fever_username                        String?  @default("")
  fever_token                           String?  @default("")
  wallabag_enabled                      Boolean? @default(false)
  wallabag_url                          String?  @default("")
  wallabag_client_id                    String?  @default("")
  wallabag_client_secret                String?  @default("")
  wallabag_username                     String?  @default("")
  wallabag_password                     String?  @default("")
  nunux_keeper_enabled                  Boolean? @default(false)
  nunux_keeper_url                      String?  @default("")
  nunux_keeper_api_key                  String?  @default("")
  telegram_bot_enabled                  Boolean? @default(false)
  telegram_bot_token                    String?  @default("")
  telegram_bot_chat_id                  String?  @default("")
  googlereader_enabled                  Boolean? @default(false)
  googlereader_username                 String?  @default("")
  googlereader_password                 String?  @default("")
  espial_enabled                        Boolean? @default(false)
  espial_url                            String?  @default("")
  espial_api_key                        String?  @default("")
  espial_tags                           String?  @default("miniflux")
  linkding_enabled                      Boolean? @default(false)
  linkding_url                          String?  @default("")
  linkding_api_key                      String?  @default("")
  wallabag_only_url                     Boolean? @default(false)
  matrix_bot_enabled                    Boolean? @default(false)
  matrix_bot_user                       String?  @default("")
  matrix_bot_password                   String?  @default("")
  matrix_bot_url                        String?  @default("")
  matrix_bot_chat_id                    String?  @default("")
  linkding_tags                         String?  @default("")
  linkding_mark_as_unread               Boolean? @default(false)
  notion_enabled                        Boolean? @default(false)
  notion_token                          String?  @default("")
  notion_page_id                        String?  @default("")
  readwise_enabled                      Boolean? @default(false)
  readwise_api_key                      String?  @default("")
  apprise_enabled                       Boolean? @default(false)
  apprise_url                           String?  @default("")
  apprise_services_url                  String?  @default("")
  shiori_enabled                        Boolean? @default(false)
  shiori_url                            String?  @default("")
  shiori_username                       String?  @default("")
  shiori_password                       String?  @default("")
  shaarli_enabled                       Boolean? @default(false)
  shaarli_url                           String?  @default("")
  shaarli_api_secret                    String?  @default("")
  webhook_enabled                       Boolean? @default(false)
  webhook_url                           String?  @default("")
  webhook_secret                        String?  @default("")
  telegram_bot_topic_id                 Int?
  telegram_bot_disable_web_page_preview Boolean? @default(false)
  telegram_bot_disable_notification     Boolean? @default(false)
  telegram_bot_disable_buttons          Boolean? @default(false)
  rssbridge_enabled                     Boolean? @default(false)
  rssbridge_url                         String?  @default("")
  omnivore_enabled                      Boolean? @default(false)
  omnivore_api_key                      String?  @default("")
  omnivore_url                          String?  @default("")
  linkace_enabled                       Boolean? @default(false)
  linkace_url                           String?  @default("")
  linkace_api_key                       String?  @default("")
  linkace_tags                          String?  @default("")
  linkace_is_private                    Boolean? @default(true)
  linkace_check_disabled                Boolean? @default(true)
  linkwarden_enabled                    Boolean? @default(false)
  linkwarden_url                        String?  @default("")
  linkwarden_api_key                    String?  @default("")
  readeck_enabled                       Boolean? @default(false)
  readeck_only_url                      Boolean? @default(false)
  readeck_url                           String?  @default("")
  readeck_api_key                       String?  @default("")
  readeck_labels                        String?  @default("")
  raindrop_enabled                      Boolean? @default(false)
  raindrop_token                        String?  @default("")
  raindrop_collection_id                String?  @default("")
  raindrop_tags                         String?  @default("")
  betula_url                            String?  @default("")
  betula_token                          String?  @default("")
  betula_enabled                        Boolean? @default(false)
  ntfy_enabled                          Boolean? @default(false)
  ntfy_url                              String?  @default("")
  ntfy_topic                            String?  @default("")
  ntfy_api_token                        String?  @default("")
  ntfy_username                         String?  @default("")
  ntfy_password                         String?  @default("")
  ntfy_icon_url                         String?  @default("")
  cubox_enabled                         Boolean? @default(false)
  cubox_api_link                        String?  @default("")
  discord_enabled                       Boolean? @default(false)
  discord_webhook_link                  String?  @default("")
  ntfy_internal_links                   Boolean? @default(false)
  slack_enabled                         Boolean? @default(false)
  slack_webhook_link                    String?  @default("")
  pushover_enabled                      Boolean? @default(false)
  pushover_user                         String?  @default("")
  pushover_token                        String?  @default("")
  pushover_device                       String?  @default("")
  pushover_prefix                       String?  @default("")
  rssbridge_token                       String?  @default("")
  karakeep_enabled                      Boolean? @default(false)
  karakeep_api_key                      String?  @default("")
  karakeep_url                          String?  @default("")
}

/// The underlying table does not contain a valid unique identifier and can therefore currently not be handled by Prisma Client.
model schema_version {
  version String

  @@ignore
}

model sessions {
  id         String   @id(map: "sessions_pkey1")
  data       Json
  created_at DateTime @default(now()) @db.Timestamptz(6)
}

model user_feed_subscriptions {
  id                Int       @id @default(autoincrement())
  user_id           Int?
  feed_id           BigInt?
  is_system_curated Boolean?  @default(false)
  subscribed_at     DateTime? @default(now()) @db.Timestamptz(6)
  feeds             feeds?    @relation(fields: [feed_id], references: [id], onDelete: Cascade, onUpdate: NoAction)
  users             users?    @relation(fields: [user_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@unique([user_id, feed_id])
}

model user_subscriptions {
  id                      Int       @id @default(autoincrement())
  user_id                 Int
  plan_id                 Int?
  status                  String    @default("free") // 'free', 'trialing', 'active', 'expired', 'canceled'
  plan_name               String?
  trial_availed           Boolean   @default(false)
  trial_start_at          DateTime? @db.Timestamptz(6)
  trial_end_at            DateTime? @db.Timestamptz(6)
  current_period_start_at DateTime? @db.Timestamptz(6)
  current_period_end_at   DateTime? @db.Timestamptz(6)
  auto_renew              Boolean   @default(false)
  created_at              DateTime  @default(now()) @db.Timestamptz(6)
  updated_at              DateTime  @default(now()) @db.Timestamptz(6)
  users                   users     @relation(fields: [user_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@unique([user_id])
  @@map("user_subscriptions")
}

model plans {
  id           Int     @id @default(autoincrement())
  market       String  // 'cn', 'global'
  name         String  // 'Pro'
  period       String  // 'monthly', 'yearly'
  price_cents  Int     // 价格（分）
  currency     String  // 'CNY', 'USD'
  is_active    Boolean @default(true)
  created_at   DateTime @default(now()) @db.Timestamptz(6)
  updated_at   DateTime @default(now()) @db.Timestamptz(6)

  @@unique([market, name, period])
  @@map("plans")
}

model orders {
  id           Int      @id @default(autoincrement())
  user_id      Int
  plan_id      Int?
  amount_cents Int
  currency     String
  provider     String   // 'wechat', 'alipay', 'stripe'
  status       String   // 'pending', 'paid', 'failed'
  created_at   DateTime @default(now()) @db.Timestamptz(6)
  users        users    @relation(fields: [user_id], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@map("orders")
}

model user_sessions {
  id         Int       @id(map: "sessions_pkey") @default(autoincrement())
  user_id    Int
  token      String    @unique(map: "sessions_token_key")
  created_at DateTime? @default(now()) @db.Timestamptz(6)
  user_agent String?
  ip         String?   @db.Inet
  users      users     @relation(fields: [user_id], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "sessions_user_id_fkey")

  @@unique([user_id, token], map: "sessions_user_id_token_key")
}

model webauthn_credentials {
  handle           Bytes     @id
  cred_id          Bytes     @unique
  user_id          Int
  public_key       Bytes
  attestation_type String    @db.VarChar(255)
  aaguid           Bytes?
  sign_count       BigInt?
  clone_warning    Boolean?
  name             String?
  added_on         DateTime? @default(now()) @db.Timestamptz(6)
  last_seen_on     DateTime? @default(now()) @db.Timestamptz(6)
  users            users     @relation(fields: [user_id], references: [id], onDelete: Cascade, onUpdate: NoAction)
}

enum entry_sorting_direction {
  asc
  desc
}

enum entry_sorting_order {
  published_at
  created_at
}

enum entry_status {
  unread
  read
  removed
}

enum webapp_display_mode {
  fullscreen
  standalone
  minimal_ui @map("minimal-ui")
  browser
}
