# News-LLM 多租户RSS新闻聚合+AI打分产品需求文档 V4.0

## 🎯 产品目标与定位

**定位**: 面向专业人士的"更少但更重要"的新闻阅读器

**核心体验**: 先看分数（默认≥3.8），再看80字摘要，决定是否点原文

**目标用户**: 投研、产品/运营、媒体、咨询、技术管理等高密度信息工作者

**用户价值**: 减少筛选时间、降低"错过重要信息"的风险

## 📋 核心功能需求 (MVP)

### 1. 付费订阅制度
**订阅源上限**:
- 免费用户: 最多10个个人订阅源
- 付费用户: 最多100个个人订阅源
- 系统公共池不占用额度，用于冷启动与对比

**高分内容延迟解锁**:
- 高分新闻(≥4.0★): 免费用户延迟解锁(D+1或T+24h)，付费/试用用户实时解锁
- 中低分新闻(<4.0★): 所有用户实时可见

**评分筛选权限**:
- 免费用户: 评分滑块上限3.9
- 付费/试用用户: 评分滑块上限5.0

### 2. 试用与定价体系
**试用机制**:
- 独立试用入口 `/trial`: 7天免费试用
- 每账号限领取1次试用
- 国内免绑卡，海外可A/B测试绑卡

**定价方案**:
- 中国市场: ¥19/月, ¥128/年
- 海外市场: $6.99/月, $59/年
- 支付方式: 中国(微信/支付宝), 海外(Stripe)

### 3. 新闻展示与交互
**单一混合列表**:
- 个人订阅 + 系统精选混合展示，无Tab切换
- 卡片要素: 分数角标、标题、来源标记、80字摘要、时间、原文链接
- 来源标记: 【订阅】/【系统】区分个人订阅与系统推荐

**锁定态交互**:
- 免费用户看到≥4.0★新闻时显示蒙层
- 角标显示"D+1解锁"或倒计时
- CTA按钮: "立即解锁(¥19/月)" + "领取7天试用"链接

### 4. AI分析与内容加工
- AI生成新闻摘要(≤80字中文)并打分(0-5分)
- 同一文章只加工一次，多用户共享结果
- 默认评分筛选≥3.8，突出高价值内容

## 🏗️ 技术方案

### 📊 数据库设计 (MVP最小集合)

#### 核心表结构
```sql
-- 新闻源表
feeds (
  id SERIAL PRIMARY KEY,
  title VARCHAR(255) NOT NULL,
  rss_url VARCHAR(500) UNIQUE NOT NULL,
  is_system BOOLEAN DEFAULT false,
  created_at TIMESTAMP DEFAULT NOW()
);

-- 用户订阅关系表
user_feed_subs (
  id SERIAL PRIMARY KEY,
  user_id INTEGER REFERENCES users(id),
  feed_id INTEGER REFERENCES feeds(id),
  created_at TIMESTAMP DEFAULT NOW(),
  UNIQUE(user_id, feed_id)
);

-- 新闻文章表 (统一加工，多用户共享)
articles (
  id SERIAL PRIMARY KEY,
  feed_id INTEGER REFERENCES feeds(id),
  title VARCHAR(500) NOT NULL,
  url VARCHAR(1000) NOT NULL,
  summary_cn TEXT, -- AI生成的80字中文摘要
  score NUMERIC(2,1) CHECK (score >= 0 AND score <= 5), -- AI评分0-5
  pub_time TIMESTAMPTZ NOT NULL,
  created_at TIMESTAMP DEFAULT NOW(),
  UNIQUE(feed_id, url)
);

-- 付费计划表
plans (
  id SERIAL PRIMARY KEY,
  market VARCHAR(10) NOT NULL, -- 'CN' | 'US'
  name VARCHAR(50) NOT NULL, -- 'Pro'
  period VARCHAR(20) NOT NULL, -- 'monthly' | 'yearly'
  price_cents INTEGER NOT NULL,
  currency VARCHAR(3) NOT NULL, -- 'CNY' | 'USD'
  is_active BOOLEAN DEFAULT true
);

-- 用户订阅状态表
user_subscriptions (
  id SERIAL PRIMARY KEY,
  user_id INTEGER REFERENCES users(id),
  plan_id INTEGER REFERENCES plans(id),
  status VARCHAR(20) NOT NULL, -- 'trialing'|'active'|'canceled'|'expired'
  trial_availed BOOLEAN DEFAULT false,
  trial_end_at TIMESTAMPTZ,
  current_period_end_at TIMESTAMPTZ,
  auto_renew BOOLEAN DEFAULT false,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- 订单支付表
orders (
  id SERIAL PRIMARY KEY,
  user_id INTEGER REFERENCES users(id),
  plan_id INTEGER REFERENCES plans(id),
  amount_cents INTEGER NOT NULL,
  currency VARCHAR(3) NOT NULL,
  provider VARCHAR(20) NOT NULL, -- 'wechat'|'alipay'|'stripe'
  status VARCHAR(20) NOT NULL, -- 'pending'|'paid'|'failed'
  created_at TIMESTAMP DEFAULT NOW()
);
```

### 🔧 技术架构
| 组件 | 技术选择 | 职责 |
|------|----------|------|
| **RSS抓取** | Miniflux + 自建RSSHub | 定时抓取RSS源，统一管理 |
| **全文提取** | trafilatura + Playwright | 提取完整新闻内容 |
| **AI处理** | 通义千问API | 生成摘要(≤80字)和评分(0-5分) |
| **数据存储** | PostgreSQL | 存储用户、订阅、新闻、付费数据 |
| **后端API** | Next.js API Routes | 提供新闻查询、订阅管理、支付接口 |
| **前端** | Next.js 15 + Tailwind + 票券风格UI | 响应式Web界面 |
| **认证** | NextAuth.js | 用户注册登录管理 |
| **支付** | 微信/支付宝 + Stripe | 国内外支付处理 |

### 🔄 数据流程
```
用户订阅RSS源 → feeds表
     ↓
Miniflux定时抓取 → 原始新闻数据
     ↓
trafilatura提取全文 → 完整内容
     ↓
AI服务(通义千问) → 生成摘要+评分
     ↓
存储到articles表 → PostgreSQL
     ↓
Next.js API → 查询接口
     ↓
前端展示 → 用户阅读
```

### 🔧 核心业务逻辑

#### 权限判定逻辑
```python
def is_locked_for_free(user, article, mode="D+1"):
    """判断免费用户是否需要锁定高分内容"""
    if article.score < 4.0:
        return False
    if user.is_pro_or_trial():
        return False
    if mode == "D+1":
        return local_date(article.pub_time, user.tz) == today(user.tz)
    # T+24h模式
    return now() < (article.pub_time + timedelta(hours=24))
```

#### 订阅源上限校验
```python
def can_subscribe_feed(user_id, feed_id):
    """检查用户是否可以订阅新源"""
    current_count = count_user_subscriptions(user_id)
    if user.is_pro_or_trial():
        return current_count < 100
    else:
        return current_count < 10
```

#### MVP必要API接口
```http
# 混合新闻列表 (含锁定态)
GET /api/articles?user_id=U&score_gte=3.8&limit=50
# 返回: id, title, score, summary_cn, pub_time, is_system, is_subscribed, locked(bool), unlock_at

# 推荐源列表
GET /api/recommend-feeds

# 订阅管理 (校验上限: 10/100)
POST /api/subscribe { user_id, rss_url }
DELETE /api/subscribe { user_id, feed_id }

# 用户订阅状态
GET /api/subscription?user_id=U

# 支付相关
POST /api/checkout { user_id, plan_id }  # 创建订单
POST /api/payment/webhook               # 支付回调
POST /api/trial/claim { user_id }       # 领取试用(每用户一次)
```

## 📱 关键页面与交互设计

### 1. 主页 - 单一混合列表 (无Tab)
**页面布局**:
```
┌─────────────────────────────────────────────────┐
│ Logo ｜ ⭐ 评分筛选(3.8-3.9/5.0) ｜ 用户头像      │
├─────────────────────────────────────────────────┤
│ 💡 今天有 X 条 ≥4.0★ 待解锁 [开通专业版][试用]   │
├─────────────────────────────────────────────────┤
│【订阅】【36Kr】【4.8★】阿里发布玄铁X芯片        │
│ 摘要：玄铁X芯片为大模型推理优化，性能提升50%…    │
│ 发布：2025-07-23 13:00  【阅读全文】             │
├─────────────────────────────────────────────────┤
│【系统】【4.2★🔒D+1解锁】OpenAI发布GPT-5        │
│ 摘要：OpenAI正式发布GPT-5模型… [蒙层遮挡]       │
│ 发布：2025-07-23 09:30                          │
│ [立即解锁 ¥19/月] [领取7天试用]                  │
└─────────────────────────────────────────────────┘
```

**卡片要素**:
- 分数角标(颜色梯度): 绿色(≥4.5) > 蓝色(4.0-4.4) > 灰色(<4.0)
- 来源标记: 【订阅】/【系统】区分个人订阅与系统推荐
- 80字摘要: 锁定态仅显示20-30字+蒙层
- 时间戳 + 阅读全文按钮

**锁定态交互**:
- 角标显示: "D+1解锁" 或 "剩X小时解锁"
- 主CTA: "立即解锁(¥19/月 / $6.99/mo)"
- 次要入口: "或领取7天免费试用" → `/trial`

### 2. 定价页 `/pricing` (默认展示真实金额)
**价格卡片**:
- 月付: ¥19/月 ($6.99/mo)
- 年付: ¥128/年 ($59/yr)
- 权益对比: 订阅源上限、高分解锁、评分筛选、历史查看
- 卡片下方: "领取7天免费试用" → `/trial`

### 3. 试用页 `/trial` (独立入口)
**页面要素**:
- 标题: "领取7天专业版试用"
- 右上角: "原价 ¥19/月 / $6.99/mo"
- 权益说明: 高分实时解锁、滑块到5.0、历史无限
- 到期规则: 国内回落免费，海外可选自动续订
- CTA: "立即开始7天试用" (每账号限1次)

## � 付费计划与权益矩阵

| 能力 | 免费 Free | 专业 Pro (订阅/试用) |
|------|-----------|---------------------|
| 个人订阅源上限 | 10 | 100 |
| 高分 ≥4.0★ | 可见但锁定(D+1/T+24h解锁) | 实时解锁 |
| 中低分 <4.0★ | 实时 | 实时 |
| 评分滑块上限 | 3.9 | 5.0 |
| 历史可看 | 近7天 | 无限期 |
| 系统公共池 | 可见(免费延迟) | 实时 |
| 广告(可选) | 可能有 | 无 |

### 定价策略 (MVP建议，用于A/B微调)
| 市场 | 月付 | 年付 | 试用/首月 |
|------|------|------|----------|
| 中国 | ¥19/月 | ¥128/年 | 7天试用(免绑)；可A/B首月¥1 |
| 海外 | $6.99/mo | $59/yr | 7-14天试用(可A/B绑卡或$0.99首月) |

**支付方式**: 中国(微信/支付宝)；海外(Stripe + IAP选做)
**文案原则**: 定价页与弹窗始终显示真实金额；试用为独立二级入口

## 📅 MVP实施计划

### P0 核心功能 (3周)
- [ ] 付费订阅数据表设计
- [ ] 高分内容延迟解锁逻辑
- [ ] 订阅源上限校验 (10/100)
- [ ] 评分滑块权限控制 (3.9/5.0)
- [ ] 锁定态UI与交互
- [ ] 试用领取机制 (每用户1次)
- [ ] 定价页与试用页开发

### P1 支付集成 (2周)
- [ ] 微信/支付宝支付集成
- [ ] Stripe海外支付集成
- [ ] 支付回调处理
- [ ] 订单状态管理
- [ ] 用户订阅状态同步

### P2 优化完善 (1周)
- [ ] 性能优化与缓存
- [ ] 移动端适配
- [ ] 用户体验优化
- [ ] A/B测试准备
- [ ] 高分内容延迟解锁逻辑
- [ ] 订阅源上限校验 (10/100)
- [ ] 评分滑块权限控制 (3.9/5.0)
- [ ] 锁定态UI与交互
- [ ] 试用领取机制 (每用户1次)
- [ ] 定价页与试用页开发

### P1 支付集成 (2周)
- [ ] 微信/支付宝支付集成
- [ ] Stripe海外支付集成
- [ ] 支付回调处理
- [ ] 订单状态管理
- [ ] 用户订阅状态同步

### P2 优化完善 (1周)
- [ ] 性能优化与缓存
- [ ] 移动端适配
- [ ] 用户体验优化
- [ ] A/B测试准备

## 🚫 非目标功能 (P1+)
以下功能不在MVP范围内，后续版本考虑：
- 关键词过滤
- 权重调参
- 推送/日报
- 队列优先级
- 每日直通券
- 后台CMS管理

## 🎯 成功指标

### 技术指标
- 页面加载时间 < 2秒
- API响应时间 < 500ms
- 高分内容延迟解锁准确率 > 99%
- 支付成功率 > 95%

### 商业指标
- 试用转化率 > 15%
- 付费用户留存率(月) > 80%
- 免费用户日活跃度 > 60%
- 订阅源使用率 > 70%

### 用户体验指标
- 界面简洁易用，学习成本低
- AI摘要准确性用户满意度 > 85%
- 评分筛选有效性 > 90%
- 锁定解锁机制用户理解度 > 95%

## 🔧 技术实现要点

### 高分延迟解锁实现
**方案A (推荐): D+1解锁**
- 当天发布的≥4.0★文章对免费用户加锁
- 次日00:00自动解锁，实现简单
- 前端显示"明日解锁"提示

**方案B: T+24h解锁**
- 发布时间+24小时后解锁
- 需要实时计算剩余时间
- 前端显示倒计时

### 订阅源管理优化
- 推荐源一键订阅功能
- RSS URL有效性检测
- 源质量评估与推荐排序
- 批量导入/导出功能

### 支付集成要点
- 国内: 微信支付/支付宝集成
- 海外: Stripe标准支付流程
- 订单状态实时同步
- 支付失败重试机制
- 退款处理流程

---

**本需求文档V4.0涵盖付费订阅MVP全部功能，为产品快速上线提供明确指导。**
