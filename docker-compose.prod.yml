version: '3.8'

services:
  # PostgreSQL数据库
  postgres:
    image: postgres:15
    container_name: news-llm-postgres
    environment:
      POSTGRES_DB: newsllm
      POSTGRES_USER: newsllm
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "127.0.0.1:5432:5432"
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 4G
          cpus: '2'
        reservations:
          memory: 2G
          cpus: '1'
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U newsllm -d newsllm"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Redis缓存和队列
  redis:
    image: redis:7-alpine
    container_name: news-llm-redis
    command: >
      redis-server 
      --requirepass ${REDIS_PASSWORD}
      --maxmemory 1gb
      --maxmemory-policy allkeys-lru
      --save 900 1
      --save 300 10
      --save 60 10000
    volumes:
      - redis_data:/data
    ports:
      - "127.0.0.1:6379:6379"
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "${REDIS_PASSWORD}", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Miniflux RSS聚合器
  miniflux:
    build: 
      context: ./miniflux-chinese
      dockerfile: Dockerfile
    container_name: news-llm-miniflux
    environment:
      DATABASE_URL: postgres://newsllm:${POSTGRES_PASSWORD}@postgres/newsllm?sslmode=disable
      RUN_MIGRATIONS: 1
      CREATE_ADMIN: 1
      ADMIN_USERNAME: admin
      ADMIN_PASSWORD: ${MINIFLUX_PASSWORD}
      BASE_URL: https://admin.your-domain.com
      LISTEN_ADDR: 0.0.0.0:8080
      WORKER_POOL_SIZE: 5
      POLLING_FREQUENCY: 60
      BATCH_SIZE: 100
      CLEANUP_FREQUENCY: 48
    expose:
      - "8080"
    depends_on:
      postgres:
        condition: service_healthy
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1'
        reservations:
          memory: 1G
          cpus: '0.5'
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/healthcheck"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # AI处理服务
  ai-service:
    build: 
      context: ./ai-service
      dockerfile: Dockerfile
    container_name: news-llm-ai
    environment:
      DATABASE_URL: postgresql://newsllm:${POSTGRES_PASSWORD}@postgres:5432/newsllm
      REDIS_URL: redis://:${REDIS_PASSWORD}@redis:6379
      QWEN_API_KEY: ${QWEN_API_KEY}
      WORKER_POOL_SIZE: 4
      MAX_CONCURRENT_REQUESTS: 10
      PROCESSING_MODE: queue
      LOG_LEVEL: INFO
      REALTIME_PROCESSING_ENABLED: true
    volumes:
      - ai_logs:/app/logs
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 6G
          cpus: '3'
        reservations:
          memory: 3G
          cpus: '1.5'
    healthcheck:
      test: ["CMD", "python", "-c", "import redis; r=redis.Redis(host='redis', password='${REDIS_PASSWORD}'); r.ping()"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 120s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # 爬虫服务
  crawler:
    build: 
      context: ./crawler
      dockerfile: Dockerfile
    container_name: news-llm-crawler
    environment:
      DATABASE_URL: postgresql://newsllm:${POSTGRES_PASSWORD}@postgres:5432/newsllm
      MINIFLUX_URL: http://miniflux:8080
      MINIFLUX_USERNAME: admin
      MINIFLUX_PASSWORD: ${MINIFLUX_PASSWORD}
      BATCH_SIZE: 50
      PROCESS_INTERVAL: 300
      LOG_LEVEL: INFO
    volumes:
      - crawler_logs:/app/logs
    depends_on:
      postgres:
        condition: service_healthy
      miniflux:
        condition: service_healthy
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1'
        reservations:
          memory: 1G
          cpus: '0.5'
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Next.js前端
  frontend:
    build: 
      context: ./news-llm-frontend
      dockerfile: Dockerfile.prod
    container_name: news-llm-frontend
    environment:
      DATABASE_URL: postgresql://newsllm:${POSTGRES_PASSWORD}@postgres:5432/newsllm
      NEXTAUTH_URL: https://your-domain.com
      NEXTAUTH_SECRET: ${NEXTAUTH_SECRET}
      NODE_ENV: production
    expose:
      - "3000"
    depends_on:
      postgres:
        condition: service_healthy
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1'
        reservations:
          memory: 512M
          cpus: '0.5'
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: news-llm-nginx
    ports:
      - "80:80"
      - "443:443"
      - "8090:8090"  # 健康检查端口
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - nginx_logs:/var/log/nginx
    depends_on:
      - frontend
      - miniflux
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8090/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  ai_logs:
    driver: local
  crawler_logs:
    driver: local
  nginx_logs:
    driver: local

networks:
  default:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
