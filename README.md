# News-LLM - AI新闻聚合平台

> **当前状态**: 🚧 开发中 | **完成度**: 70% | **最后更新**: 2025-07-22

一个基于AI的智能新闻聚合平台，提供多源新闻抓取、智能摘要生成、个性化推荐和付费订阅服务。

## 📊 项目进度概览

| 模块 | 状态 | 完成度 | 说明 |
|------|------|--------|------|
| 🗄️ **数据库设计** | ✅ 完成 | 100% | PostgreSQL + pgvector，表结构完整 |
| 🕷️ **爬虫系统** | ✅ 完成 | 95% | RSS抓取、去重、内容处理 |
| 🤖 **AI服务** | ✅ 完成 | 90% | 分类、摘要、评分功能 |
| 🔧 **后端API** | 🔄 进行中 | 60% | FastAPI框架，部分接口完成 |
| 🎨 **前端界面** | 🔄 进行中 | 20% | Next.js 14 + TypeScript + Tailwind CSS |
| 📱 **移动端** | ❌ 未开始 | 0% | 计划开发微信小程序 |
| 📰 **Miniflux本地版** | ✅ 完成 | 100% | 支持中文界面的RSS聚合器 |
| 🔧 **本地部署** | ✅ 完成 | 100% | 本地服务配置完整 |
| 📈 **监控系统** | ✅ 完成 | 80% | Prometheus + Grafana |

## 🚀 项目特色

- **🔍 Folo发现**: 集成开源Folo，解决"用户不会找源"问题，直觉式订阅
- **📰 自建RSSHub**: 稳定可控的RSS生成，支持微信公众号、B站、微博等
- **🤖 AI智能处理**: 集成通义千问API，生成中英双语摘要和1-5星评分
- **🔍 智能过滤**: 按分类/关键词/重要性分数过滤，付费用户享受更多权限
- **📊 向量搜索**: 使用PostgreSQL + pgvector实现语义搜索和推荐
- **⚡ 全链路开源**: Miniflux + Folo + RSSHub + FastAPI，90%轮子现成
- **🎨 现代化界面**: Next.js 14 Web端 + uni-app小程序，支持推送
- **🇨🇳 本地化合规**: 支持ICP备案、微信支付、公众号推送等

## 🏗️ 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   新闻源抓取     │    │   内容处理      │    │   用户服务      │
│                │    │                │    │                │
│ • Miniflux     │───▶│ • AI摘要生成    │───▶│ • 用户认证      │
│ • RSSHub       │    │ • 重要性评分    │    │ • 个性化推荐    │
│ • FeedX        │    │ • 向量化存储    │    │ • 付费订阅      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   数据存储      │    │   API服务       │    │   前端界面      │
│                │    │                │    │                │
│ • PostgreSQL   │◀───│ • FastAPI      │◀───│ • Next.js Web   │
│ • Redis缓存    │    │ • JWT认证      │    │ • 微信小程序    │
│ • 向量数据库    │    │ • RESTful API  │    │ • 后台管理      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 📋 功能实现状态

### ✅ 已完成功能

#### 🗄️ 数据层
- [x] PostgreSQL数据库设计（用户、文章、订阅等表）
- [x] pgvector向量搜索支持
- [x] Redis缓存配置
- [x] 数据库迁移脚本

#### 🕷️ 爬虫系统
- [x] RSS源配置管理（20个新闻源）
- [x] 多源新闻抓取（RSS、RSSHub支持）
- [x] 全文内容提取（trafilatura）
- [x] 智能去重（MD5+相似度算法）
- [x] 内容清理和预处理

#### 🤖 AI服务
- [x] 自动分类器（基于关键词和规则）
- [x] 关键词提取
- [x] 实体识别
- [x] 通义千问API集成（摘要生成）
- [x] 重要性评分算法

#### 🔧 基础设施
- [x] 本地服务配置
- [x] 环境变量管理
- [x] 日志系统
- [x] 监控配置（Prometheus + Grafana）

### 🔄 进行中功能

#### 🔧 后端API
- [x] FastAPI框架搭建
- [x] 数据模型定义
- [x] 基础配置和中间件
- [ ] 用户认证系统（JWT）
- [ ] 文章API接口
- [ ] 订阅管理API
- [ ] 支付集成

### ❌ 待开发功能

#### 🎨 前端应用
- [ ] Next.js 14项目初始化
- [ ] 用户界面设计
- [ ] 新闻列表和详情页
- [ ] 用户登录注册
- [ ] 订阅管理界面
- [ ] 响应式设计

#### 📱 移动端
- [ ] 微信小程序开发
- [ ] 移动端适配
- [ ] 推送通知

#### 🔧 高级功能
- [ ] 个性化推荐算法
- [ ] 向量搜索实现
- [ ] 用户行为分析
- [ ] A/B测试框架

## 🛠️ 技术栈

### 后端服务
- **API框架**: FastAPI (Python)
- **数据库**: PostgreSQL + pgvector
- **缓存**: Redis
- **消息队列**: Celery + Redis
- **AI服务**: LangChain + 通义千问API

### 新闻抓取
- **RSS聚合**: Miniflux
- **伪RSS生成**: RSSHub
- **全文抓取**: trafilatura + Playwright

### 前端应用
- **Web端**: Next.js 14 + Tailwind CSS + shadcn/ui
- **移动端**: 微信小程序 (uni-app)
- **后台管理**: Directus CMS

### 基础设施
- **部署**: 本地服务 + 公共RSSHub实例
- **监控**: Prometheus + Grafana
- **支付**: Ping++ SDK
- **认证**: Supabase Auth

## 🚀 快速开始

### 🎯 本地部署（推荐）

#### 环境要求
- **Go 1.24+**: 编译Miniflux
- **Python 3.9+**: 后端服务
- **PostgreSQL 14+**: 数据库
- **Node.js 18+**: 前端开发（可选）

#### 一键启动
```bash
# 1. 克隆项目
git clone https://github.com/loiasdi/news.git
cd news

# 2. 一键启动所有服务
./start-local.sh
```

启动脚本会自动：
- ✅ 检查系统要求
- ✅ 启动PostgreSQL数据库
- ✅ 编译并启动Miniflux（端口8080）
- ✅ 启动爬虫服务
- ✅ 启动AI处理服务
- ✅ 启动后端API（端口8000）

#### 访问应用
- **Miniflux界面**: http://localhost:8080
  - 账号: admin / miniflux_admin_password
  - 设置中文: Settings → 偏好设置 → 语言 → 简体中文
- **后端API**: http://localhost:8000/docs
- **前端界面**: http://localhost:3000（开发中）

#### 停止服务
```bash
./stop-local.sh
```

### 📋 环境要求
- Python 3.9+
- PostgreSQL 14+
- Go 1.21+ (Miniflux编译)
- Node.js 18+ (前端开发时需要)

### 当前可运行的功能

#### 1. 启动数据库和基础服务
```bash
# 克隆项目
git clone https://github.com/loiasdi/news.git
cd news

# 配置环境变量
cp .env.example .env
# 编辑 .env 文件，配置数据库、API密钥等

# 启动数据库
./start_db.sh
```

#### 2. 初始化数据库
```bash
# 创建表结构和示例数据
python init_db.py
```

#### 3. 运行爬虫测试
```bash
# 激活虚拟环境
source crawler/venv/bin/activate

# 运行爬虫获取新闻
cd crawler
python main.py
```

#### 4. 测试AI服务
```bash
# 测试文章分类和处理
python scripts/test_ai_service.py
```

### 🚧 开发中的功能

#### 后端API服务（部分可用）
```bash
# 安装后端依赖（正在进行中）
cd backend
pip install -r requirements.txt

# 启动API服务（开发中）
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

#### 前端应用（待开发）
```bash
# 前端项目尚未初始化
# 计划使用 Next.js 14 + Tailwind CSS
```

### 📊 当前数据状态

运行上述步骤后，你将获得：
- ✅ **数据库**: 20个新闻源配置
- ✅ **测试数据**: 16篇真实新闻文章
- ✅ **分类统计**: 国际8篇、科技7篇、汽车1篇
- ✅ **AI处理**: 文章分类和关键词提取

### 🔗 服务访问地址

| 服务 | 地址 | 状态 | 说明 |
|------|------|------|------|
| 数据库 | localhost:5432 | ✅ 运行中 | PostgreSQL |
| Redis | localhost:6379 | 🔄 配置中 | 缓存服务 |
| 后端API | localhost:8000 | 🔄 开发中 | FastAPI |
| 前端应用 | localhost:3000 | ❌ 待开发 | Next.js |
| 监控面板 | localhost:3001 | 🔄 配置中 | Grafana |

## 📁 项目结构

```
news-llm/
├── backend/                 # 后端API服务
│   ├── app/
│   │   ├── api/            # API路由
│   │   ├── core/           # 核心配置
│   │   ├── models/         # 数据模型
│   │   ├── services/       # 业务逻辑
│   │   └── utils/          # 工具函数
│   ├── requirements.txt
│   └── main.py
├── frontend/               # 前端应用
│   ├── components/         # React组件
│   ├── pages/             # 页面路由
│   ├── styles/            # 样式文件
│   ├── utils/             # 工具函数
│   └── package.json
├── crawler/               # 新闻抓取服务
│   ├── scrapers/          # 抓取器
│   ├── processors/        # 内容处理
│   └── config/            # 配置文件
├── ai-service/            # AI摘要服务
│   ├── summarizer/        # 摘要生成
│   ├── classifier/        # 分类器
│   └── scorer/            # 评分器
├── docs/                  # 项目文档
├── scripts/               # 部署脚本
├── .env.example
└── README.md
```

## 🔧 配置说明

### 环境变量
```bash
# 数据库配置
DATABASE_URL=postgresql://user:password@localhost:5432/newsllm
REDIS_URL=redis://localhost:6379

# AI服务配置
QWEN_API_KEY=your_qwen_api_key
QWEN_API_URL=https://dashscope.aliyuncs.com/api/v1/

# 认证配置
JWT_SECRET_KEY=your_jwt_secret
SUPABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_supabase_anon_key

# 支付配置
PINGPP_API_KEY=your_pingpp_api_key
PINGPP_APP_ID=your_pingpp_app_id

# 微信配置
WECHAT_APP_ID=your_wechat_app_id
WECHAT_APP_SECRET=your_wechat_app_secret
```

## 📊 监控和运维

### 健康检查
- API健康检查: `/health`
- 数据库连接状态: `/health/db`
- Redis连接状态: `/health/redis`
- 新闻源状态: `/health/sources`

### 监控指标
- 新闻抓取成功率
- AI摘要生成延迟
- 用户活跃度
- 系统资源使用率

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 🔄 开发计划

### 近期目标（1-2周）
1. **完成后端API开发**
   - [ ] 用户认证系统
   - [ ] 文章CRUD接口
   - [ ] 订阅管理API

2. **启动前端开发**
   - [ ] Next.js项目初始化
   - [ ] 基础UI组件
   - [ ] 新闻列表页面

### 中期目标（1个月）
1. **功能完善**
   - [ ] 用户注册登录
   - [ ] 个性化推荐
   - [ ] 支付系统集成

2. **移动端开发**
   - [ ] 微信小程序
   - [ ] 响应式设计

### 长期目标（3个月）
1. **高级功能**
   - [ ] 向量搜索
   - [ ] 智能推荐算法
   - [ ] 多语言支持

## 🤝 贡献指南

项目目前处于快速开发阶段，欢迎贡献代码！

### 当前急需帮助的领域：
- 🎨 **前端开发**: Next.js + Tailwind CSS
- 📱 **移动端**: 微信小程序开发
- 🔧 **后端API**: FastAPI接口完善
- 🧪 **测试**: 单元测试和集成测试

### 参与方式：
1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📞 联系我们

- 项目主页: https://github.com/loiasdi/news
- 问题反馈: https://github.com/loiasdi/news/issues
- 开发讨论: 欢迎提Issue讨论功能需求

---

⭐ 如果这个项目对你有帮助，请给我们一个星标！项目正在快速开发中，期待你的参与！
