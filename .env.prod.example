# ===========================================
# News-LLM 生产环境配置文件
# ===========================================

# ===========================================
# 数据库配置
# ===========================================
POSTGRES_PASSWORD=your_super_secure_postgres_password_here
DATABASE_URL=***************************************************************************/newsllm

# ===========================================
# Redis配置
# ===========================================
REDIS_PASSWORD=your_super_secure_redis_password_here
REDIS_URL=redis://:your_super_secure_redis_password_here@redis:6379

# ===========================================
# Miniflux配置
# ===========================================
MINIFLUX_PASSWORD=your_super_secure_miniflux_password_here
MINIFLUX_URL=http://miniflux:8080
MINIFLUX_USERNAME=admin

# ===========================================
# Next.js前端配置
# ===========================================
NEXTAUTH_SECRET=your_super_secure_nextauth_secret_here_min_32_chars
NEXTAUTH_URL=https://your-domain.com
NODE_ENV=production

# ===========================================
# AI服务配置
# ===========================================
QWEN_API_KEY=your_qwen_api_key_here
QWEN_API_URL=https://dashscope.aliyuncs.com/api/v1/
QWEN_MODEL=qwen-plus
QWEN_MAX_TOKENS=2000
QWEN_TEMPERATURE=0.1

# ===========================================
# AI处理配置
# ===========================================
REALTIME_PROCESSING_ENABLED=true
PROCESSING_MODE=queue
WORKER_POOL_SIZE=4
MAX_CONCURRENT_REQUESTS=10
BATCH_SIZE=10
PROCESSING_DELAY=5

# ===========================================
# 队列配置
# ===========================================
QUEUE_MAX_RETRIES=3
QUEUE_RETRY_DELAY=60
QUEUE_HIGH_PRIORITY_THRESHOLD=7
QUEUE_CLEANUP_INTERVAL=3600

# ===========================================
# 内容处理配置
# ===========================================
CONTENT_MAX_LENGTH=8000
CONTENT_MIN_LENGTH=100
SUMMARY_MAX_LENGTH=80
SUMMARY_MIN_LENGTH=20

# ===========================================
# 性能配置
# ===========================================
MEMORY_LIMIT_MB=1024
CPU_LIMIT_PERCENT=80
REQUEST_TIMEOUT=30
MAX_RETRIES=3
RETRY_DELAY=1.0

# ===========================================
# 缓存配置
# ===========================================
CACHE_ENABLED=true
CACHE_TTL=3600

# ===========================================
# 日志配置
# ===========================================
LOG_LEVEL=INFO
LOG_PROCESSING_STATS=true
STATS_UPDATE_INTERVAL=60
HEALTH_CHECK_INTERVAL=30

# ===========================================
# 监控配置
# ===========================================
ENABLE_QUALITY_CHECK=true
MIN_QUALITY_SCORE=0.6
AUTO_RESTART_ON_FAILURE=true
MAX_RESTART_ATTEMPTS=3
RESTART_DELAY=30

# ===========================================
# 触发器配置
# ===========================================
TRIGGER_ENABLED=true
TRIGGER_BATCH_SIZE=5

# ===========================================
# 爬虫配置
# ===========================================
USE_MINIFLUX=true
EXTRACT_FULLTEXT=true
CRAWLER_BATCH_SIZE=20
PROCESS_INTERVAL=300

# ===========================================
# 域名配置
# ===========================================
# 请替换为你的实际域名
DOMAIN=your-domain.com
ADMIN_DOMAIN=admin.your-domain.com

# ===========================================
# SSL证书路径（如果使用自签名证书）
# ===========================================
SSL_CERT_PATH=/etc/nginx/ssl/cert.pem
SSL_KEY_PATH=/etc/nginx/ssl/key.pem

# ===========================================
# 备份配置
# ===========================================
BACKUP_ENABLED=true
BACKUP_INTERVAL=daily
BACKUP_RETENTION_DAYS=30
BACKUP_PATH=/var/backups/news-llm

# ===========================================
# 安全配置
# ===========================================
# 允许的IP地址（用于管理界面访问限制）
ALLOWED_IPS=127.0.0.1,***********/24

# CORS配置
CORS_ORIGINS=https://your-domain.com,https://admin.your-domain.com

# ===========================================
# 第三方服务配置（可选）
# ===========================================
# Sentry错误监控
SENTRY_DSN=

# 邮件服务配置
SMTP_HOST=
SMTP_PORT=587
SMTP_USER=
SMTP_PASSWORD=
SMTP_FROM=<EMAIL>

# ===========================================
# 开发/调试配置（生产环境请设为false）
# ===========================================
DEBUG=false
VERBOSE_LOGGING=false
ENABLE_PROFILING=false

# ===========================================
# 特性开关
# ===========================================
FEATURE_USER_REGISTRATION=true
FEATURE_SOCIAL_LOGIN=false
FEATURE_EMAIL_NOTIFICATIONS=false
FEATURE_PUSH_NOTIFICATIONS=false

# ===========================================
# 配置说明
# ===========================================
# 1. 请将所有 "your_xxx_here" 替换为实际的值
# 2. 密码建议使用 openssl rand -base64 32 生成
# 3. API密钥请从对应服务商获取
# 4. 域名请替换为你的实际域名
# 5. 生产环境请确保所有密码足够复杂
# 6. 建议定期轮换密码和API密钥
