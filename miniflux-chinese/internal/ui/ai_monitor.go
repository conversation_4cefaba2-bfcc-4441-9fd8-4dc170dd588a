// SPDX-FileCopyrightText: Copyright The Miniflux Authors. All rights reserved.
// SPDX-License-Identifier: Apache-2.0

package ui // import "miniflux.app/v2/internal/ui"

import (
	"context"
	"encoding/json"
	"log/slog"
	"net/http"
	"strconv"
	"time"

	"github.com/go-redis/redis/v8"
	"miniflux.app/v2/internal/http/request"
	"miniflux.app/v2/internal/http/response/html"
	"miniflux.app/v2/internal/model"
	"miniflux.app/v2/internal/ui/session"
	"miniflux.app/v2/internal/ui/view"
)

// showAIMonitorPage 显示AI处理监控页面
func (h *handler) showAIMonitorPage(w http.ResponseWriter, r *http.Request) {
	user, err := h.store.UserByID(request.UserID(r))
	if err != nil {
		html.ServerError(w, r, err)
		return
	}

	// 获取查询参数
	status := r.URL.Query().Get("status")
	if status == "" {
		status = "all"
	}

	// 分页参数
	offset, err := strconv.Atoi(r.URL.Query().Get("offset"))
	if err != nil || offset < 0 {
		offset = 0
	}
	limit := 20 // 每页显示20篇文章

	// 获取AI处理统计信息
	stats, err := h.store.GetAIProcessingStats(user.ID)
	if err != nil {
		html.ServerError(w, r, err)
		return
	}

	// 获取带AI信息的文章列表
	entries, err := h.store.GetEntriesWithAIInfo(user.ID, status, offset, limit)
	if err != nil {
		html.ServerError(w, r, err)
		return
	}

	// 获取总数用于分页
	total, err := h.store.CountEntriesWithAIInfo(user.ID, status)
	if err != nil {
		html.ServerError(w, r, err)
		return
	}

	// 计算分页信息
	pagination := map[string]interface{}{
		"current_page": (offset / limit) + 1,
		"total_pages":  (total + int64(limit) - 1) / int64(limit),
		"has_prev":     offset > 0,
		"has_next":     offset+limit < int(total),
		"prev_offset":  offset - limit,
		"next_offset":  offset + limit,
	}

	sess := session.New(h.store, request.SessionID(r))
	view := view.New(h.tpl, r, sess)
	view.Set("user", user)
	view.Set("countUnread", h.store.CountUnreadEntries(user.ID))
	view.Set("countErrorFeeds", h.store.CountUserFeedsWithErrors(user.ID))
	view.Set("entries", entries)
	view.Set("stats", stats)
	view.Set("status", status)
	view.Set("total", total)
	view.Set("pagination", pagination)
	view.Set("menu", "ai_monitor")

	html.OK(w, r, view.Render("ai_monitor"))
}

// showAIEntryDetails 显示单个文章的AI处理详情
func (h *handler) showAIEntryDetails(w http.ResponseWriter, r *http.Request) {
	user, err := h.store.UserByID(request.UserID(r))
	if err != nil {
		html.ServerError(w, r, err)
		return
	}

	entryID := request.RouteInt64Param(r, "entryID")
	builder := h.store.NewEntryQueryBuilder(user.ID)
	builder.WithEntryID(entryID)
	builder.WithoutStatus(model.EntryStatusRemoved)

	entry, err := builder.GetEntry()
	if err != nil {
		html.ServerError(w, r, err)
		return
	}

	if entry == nil {
		html.NotFound(w, r)
		return
	}

	sess := session.New(h.store, request.SessionID(r))
	view := view.New(h.tpl, r, sess)
	view.Set("user", user)
	view.Set("countUnread", h.store.CountUnreadEntries(user.ID))
	view.Set("countErrorFeeds", h.store.CountUserFeedsWithErrors(user.ID))
	view.Set("entry", entry)
	view.Set("menu", "ai_monitor")

	html.OK(w, r, view.Render("ai_entry_details"))
}

// reprocessAIEntry 重新处理文章的AI分析
func (h *handler) reprocessAIEntry(w http.ResponseWriter, r *http.Request) {
	user, err := h.store.UserByID(request.UserID(r))
	if err != nil {
		html.ServerError(w, r, err)
		return
	}

	entryID := request.RouteInt64Param(r, "entryID")

	// 验证文章所有权
	builder := h.store.NewEntryQueryBuilder(user.ID)
	builder.WithEntryID(entryID)
	builder.WithoutStatus(model.EntryStatusRemoved)

	entry, err := builder.GetEntry()
	if err != nil {
		html.ServerError(w, r, err)
		return
	}

	if entry == nil {
		html.NotFound(w, r)
		return
	}

	// 重置AI处理状态，触发重新处理
	err = h.store.ResetAIProcessingStatus(entryID)
	if err != nil {
		html.ServerError(w, r, err)
		return
	}

	// 触发AI处理
	err = h.triggerAIProcessing(entry)
	if err != nil {
		slog.Error("Failed to trigger AI processing", "error", err)
		// 不阻止页面显示，只记录错误
	}

	// 返回JSON响应，不重定向
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)

	var message string
	if entry.AIProcessed {
		message = "文章已标记为重新处理，AI将重新分析此文章"
	} else {
		message = "文章已加入处理队列，AI正在分析中..."
	}

	response := map[string]interface{}{
		"success": true,
		"message": message,
		"entry_id": entryID,
	}

	json.NewEncoder(w).Encode(response)
}

// triggerAIProcessing 触发AI处理
func (h *handler) triggerAIProcessing(entry *model.Entry) error {
	// 创建Redis客户端
	rdb := redis.NewClient(&redis.Options{
		Addr:     "localhost:6379",
		Password: "", // 如果有密码请设置
		DB:       0,  // 使用默认数据库
	})
	defer rdb.Close()

	// 准备文章数据
	articleData := map[string]interface{}{
		"id":           entry.ID,
		"title":        entry.Title,
		"content":      entry.Content,
		"url":          entry.URL,
		"author":       entry.Author,
		"published_at": entry.Date,
		"tags":         []string{}, // 如果有标签可以添加
	}

	// 准备队列项
	queueItem := map[string]interface{}{
		"article_data": articleData,
		"enqueued_at":  time.Now().Unix(),
		"source":       "miniflux_manual",
		"priority":     5, // 手动触发的文章给中等优先级
	}

	// 序列化为JSON
	queueJSON, err := json.Marshal(queueItem)
	if err != nil {
		return err
	}

	// 加入Redis队列
	ctx := context.Background()
	err = rdb.LPush(ctx, "queue:new_articles", string(queueJSON)).Err()
	if err != nil {
		return err
	}

	slog.Info("Added article to AI processing queue", "entry_id", entry.ID, "queue", "queue:new_articles")
	return nil
}




